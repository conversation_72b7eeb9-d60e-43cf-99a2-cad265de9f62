#!/bin/bash

# تعيين الألوان
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة لطباعة رسائل ملونة
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

clear

echo "================================================"
echo "           QMS Flow - نظام إدارة الجودة"
echo "================================================"
echo ""
echo "جاري تشغيل التطبيق على الشبكة المحلية..."
echo ""

# التحقق من وجود Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js غير مثبت على النظام"
    echo "يرجى تثبيت Node.js من: https://nodejs.org"
    echo ""
    read -p "اضغط Enter للمتابعة..."
    exit 1
fi

# التحقق من وجود npm
if ! command -v npm &> /dev/null; then
    print_error "npm غير متاح"
    echo "يرجى إعادة تثبيت Node.js"
    echo ""
    read -p "اضغط Enter للمتابعة..."
    exit 1
fi

# التحقق من وجود package.json
if [ ! -f "package.json" ]; then
    print_error "لم يتم العثور على package.json"
    echo "تأكد من تشغيل الاسكريبت في مجلد المشروع الصحيح"
    echo ""
    read -p "اضغط Enter للمتابعة..."
    exit 1
fi

print_success "Node.js متاح - الإصدار: $(node --version)"
print_success "npm متاح - الإصدار: $(npm --version)"
echo ""

# الحصول على عنوان IP المحلي
echo "جاري الحصول على عنوان IP المحلي..."

# محاولة الحصول على IP بطرق مختلفة
LOCAL_IP=""

# الطريقة الأولى - Linux
if command -v hostname &> /dev/null; then
    LOCAL_IP=$(hostname -I | awk '{print $1}' 2>/dev/null)
fi

# الطريقة الثانية - macOS
if [ -z "$LOCAL_IP" ] && command -v ifconfig &> /dev/null; then
    LOCAL_IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
fi

# الطريقة الثالثة - عامة
if [ -z "$LOCAL_IP" ]; then
    LOCAL_IP=$(ip route get ******* 2>/dev/null | awk '{print $7}' | head -1)
fi

# إذا لم نجد IP، استخدم localhost
if [ -z "$LOCAL_IP" ]; then
    print_warning "لم يتم العثور على عنوان IP المحلي"
    LOCAL_IP="localhost"
else
    print_success "عنوان IP المحلي: $LOCAL_IP"
fi

echo ""
echo "================================================"
echo "                معلومات الاتصال"
echo "================================================"
echo ""
echo "🌐 الوصول المحلي:     http://localhost:5173"
echo "🌐 الوصول من الشبكة:  http://$LOCAL_IP:5173"
echo ""
echo "ملاحظة: تأكد من أن جدار الحماية يسمح بالاتصالات"
echo "         على المنفذ 5173"
echo ""
echo "================================================"
echo ""

# تثبيت التبعيات إذا لم تكن موجودة
if [ ! -d "node_modules" ]; then
    echo "📦 جاري تثبيت التبعيات..."
    echo ""
    npm install
    if [ $? -ne 0 ]; then
        print_error "فشل في تثبيت التبعيات"
        echo ""
        read -p "اضغط Enter للمتابعة..."
        exit 1
    fi
    print_success "تم تثبيت التبعيات بنجاح"
    echo ""
fi

echo "🚀 جاري تشغيل التطبيق..."
echo ""
echo "للإيقاف: اضغط Ctrl+C"
echo ""

# تشغيل التطبيق مع إعدادات الشبكة
npm run dev -- --host 0.0.0.0 --port 5173

echo ""
echo "تم إيقاف التطبيق"
read -p "اضغط Enter للمتابعة..."
