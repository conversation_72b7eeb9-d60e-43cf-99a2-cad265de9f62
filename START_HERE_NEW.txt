🚀 ابدأ هنا - QMS Flow مع قاعدة البيانات المشتركة
================================================

مرحباً! هذا دليل سريع لتشغيل نظام إدارة الجودة مع قاعدة بيانات مشتركة.

🎯 الجديد: البيانات الآن مشتركة بين جميع الأجهزة!

📋 الخطوات السريعة (الطريقة الجديدة):
===================================
1️⃣ تحقق من النظام:
   انقر مرتين على: check-system.bat

2️⃣ شغل النظام الكامل:
   انقر مرتين على: start-full-network.bat
   (ستظهر نافذتان: قاعدة البيانات + التطبيق)

3️⃣ اعرف عنوان IP:
   انقر مرتين على: get-ip.bat

4️⃣ ادخل للتطبيق:
   من نفس الجهاز: http://localhost:5173
   من أجهزة أخرى: http://[IP-ADDRESS]:5173

📁 الملفات المهمة:
==================
📁 start-full-network.bat      ← تشغيل النظام الكامل (موصى به)
📁 simple-network.bat          ← تشغيل بسيط (بدون قاعدة بيانات مشتركة)
📁 get-ip.bat                  ← معرفة عنوان IP
📁 check-system.bat            ← فحص النظام
📄 HOW_TO_RUN_SHARED_DATABASE.txt ← دليل مفصل للنظام الجديد
📄 db.json                    ← ملف قاعدة البيانات المشتركة

✨ مميزات النظام الجديد:
=======================
✅ المستخدمون المضافون يظهرون على جميع الأجهزة
✅ المستندات والملفات متاحة للجميع
✅ تسجيل الدخول يعمل من أي جهاز
✅ البيانات محفوظة حتى بعد إغلاق التطبيق
✅ يعمل بدون إنترنت (شبكة محلية فقط)

🔧 إذا لم يعمل:
===============
- تأكد من تثبيت Node.js
- شغل check-system.bat للتشخيص
- اقرأ ملف HOW_TO_RUN_SHARED_DATABASE.txt

💡 نصائح مهمة:
==============
- استخدم start-full-network.bat للحصول على جميع المميزات
- اترك نافذتي الخادم مفتوحتين (لا تغلقهما)
- لإيقاف النظام: Ctrl+C في كلا النافذتين
- احتفظ بنسخة احتياطية من ملف db.json

🆚 الفرق بين الطريقتين:
========================
start-full-network.bat:
- البيانات مشتركة بين جميع الأجهزة ✅
- المستخدمون يظهرون على كل الأجهزة ✅
- يحتاج نافذتين

simple-network.bat:
- البيانات محلية فقط ❌
- كل جهاز له بيانات منفصلة ❌
- يحتاج نافذة واحدة

للمساعدة المفصلة، اقرأ: HOW_TO_RUN_SHARED_DATABASE.txt
