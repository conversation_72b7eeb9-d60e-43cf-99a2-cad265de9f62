@echo off
chcp 65001 >nul
echo ========================================
echo    تشغيل نظام إدارة الجودة مع قاعدة البيانات
echo ========================================
echo.

echo جاري التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت على النظام
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js متوفر
echo.

echo جاري تثبيت المتطلبات...
call npm install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

echo ✅ تم تثبيت المتطلبات بنجاح
echo.

echo جاري الحصول على عنوان IP...
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set "ip=%%b"
        goto :found
    )
)
:found

echo ✅ عنوان IP: %ip%
echo.

echo 🚀 جاري تشغيل قاعدة البيانات والتطبيق...
echo.
echo سيتم تشغيل:
echo - قاعدة البيانات على المنفذ 3001
echo - التطبيق على المنفذ 5173
echo.
echo التطبيق متاح على:
echo - محلياً: http://localhost:5173
echo - على الشبكة: http://%ip%:5173
echo.
echo قاعدة البيانات متاحة على:
echo - محلياً: http://localhost:3001
echo - على الشبكة: http://%ip%:3001
echo.
echo اضغط Ctrl+C لإيقاف التطبيق
echo.

echo جاري تشغيل قاعدة البيانات...
start "Database Server" cmd /k "npm run db"

echo انتظار 3 ثوانٍ لتشغيل قاعدة البيانات...
timeout /t 3 /nobreak >nul

echo جاري تشغيل التطبيق...
npm run dev:network

pause
