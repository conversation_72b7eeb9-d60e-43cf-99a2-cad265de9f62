# 🌐 تشغيل QMS Flow على الشبكة المحلية

## 🚀 التشغيل السريع

### الطريقة الأولى: الاسكريبت التلقائي الكامل
```bash
# انقر نقراً مزدوجاً على الملف
start-network.bat
```

### الطريقة الثانية: الاسكريبت السريع
```bash
# انقر نقراً مزدوجاً على الملف
network-quick.bat
```

### الطريقة الثالثة: الأوامر المباشرة
```bash
# تشغيل على الشبكة المحلية
npm run dev:network

# أو الأمر الكامل
npm run dev -- --host 0.0.0.0 --port 5173
```

## 📱 الوصول للتطبيق

بعد التشغيل، يمكن الوصول للتطبيق من:

### 💻 نفس الجهاز:
```
http://localhost:5173
```

### 📱 الأجهزة الأخرى في الشبكة:
```
http://[عنوان-IP]:5173
```

**مثال:**
```
http://*************:5173
```

## 🔍 معرفة عنوان IP المحلي

### Windows:
1. اضغط `Win + R`
2. اكتب `cmd` واضغط Enter
3. اكتب `ipconfig` واضغط Enter
4. ابحث عن "IPv4 Address"

### أو من الاسكريبت:
الاسكريبت سيعرض عنوان IP تلقائياً عند التشغيل

## 📋 متطلبات التشغيل

✅ **Node.js مثبت**  
✅ **npm متاح**  
✅ **جدار الحماية يسمح بالمنفذ 5173**  
✅ **جميع الأجهزة في نفس الشبكة**  

## 🛠️ استكشاف الأخطاء

### المشكلة: لا يمكن الوصول من أجهزة أخرى
**الحل:**
1. تأكد من تشغيل الاسكريبت الصحيح
2. تحقق من إعدادات جدار الحماية
3. تأكد أن الأجهزة في نفس الشبكة

### المشكلة: المنفذ مستخدم
**الحل:**
```bash
# استخدم منفذ مختلف
npm run dev -- --host 0.0.0.0 --port 3000
```

## 🔒 ملاحظات الأمان

⚠️ **تحذير:**
- استخدم فقط في الشبكات الآمنة
- لا تعرض على الإنترنت العام
- أغلق التطبيق عند عدم الحاجة

## 🎯 الخلاصة

1. **شغل الاسكريبت**: `start-network.bat`
2. **احصل على عنوان IP** من الاسكريبت
3. **شارك العنوان** مع الأجهزة الأخرى
4. **استمتع بالوصول من أي جهاز في الشبكة!**

---

💡 **نصيحة:** احفظ عنوان IP في المفضلة لسهولة الوصول لاحقاً
