import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Phone, Mail, Award, BookOpen, Code, Briefcase, Calendar } from 'lucide-react';

const About: React.FC = () => {
  return (
    <div className="space-y-8">
      <h1 className="text-2xl font-bold">حول البرنامج</h1>
      
      <Card>
        <CardHeader>
          <CardTitle>نظام إدارة مستندات الجودة الداخلي</CardTitle>
          <CardDescription>
            نظام متكامل لإدارة وتنظيم وثائق ومستندات الجودة داخل المؤسسات
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex flex-col md:flex-row gap-6">
            <div className="md:w-1/3">
              <div className="flex flex-col items-center text-center space-y-4">
                <Avatar className="h-32 w-32">
                  <AvatarImage src="/engineer.png" alt="م. كريم وهيب" />
                  <AvatarFallback>كو</AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="text-xl font-bold">م. كريم وهيب</h3>
                  <p className="text-muted-foreground">مهندس جودة ومطور برمجيات</p>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  <span dir="ltr">01159296333</span>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  <span><EMAIL></span>
                </div>
              </div>
            </div>
            
            <Separator className="md:hidden" />
            
            <div className="md:w-2/3 space-y-6">
              <div>
                <h3 className="text-lg font-semibold flex items-center gap-2 mb-3">
                  <Award className="h-5 w-5 text-primary" />
                  <span>نبذة مهنية</span>
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  مهندس جودة محترف ومطور برمجيات متخصص في تصميم وتطوير أنظمة إدارة الجودة والحلول البرمجية المتكاملة. 
                  أمتلك خبرة واسعة في مجال هندسة الجودة وتطبيق المعايير الدولية مثل الأيزو 9001، بالإضافة إلى مهارات متقدمة 
                  في تطوير تطبيقات الويب باستخدام أحدث التقنيات. أسعى دائمًا لتقديم حلول مبتكرة تجمع بين الجودة والتكنولوجيا 
                  لتحسين كفاءة العمليات وتعزيز الأداء المؤسسي.
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-lg font-semibold flex items-center gap-2 mb-3">
                    <BookOpen className="h-5 w-5 text-primary" />
                    <span>خبرات هندسة الجودة</span>
                  </h3>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <Badge variant="outline" className="mt-0.5">ISO 9001</Badge>
                      <span>تطبيق وتوثيق أنظمة إدارة الجودة</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Badge variant="outline" className="mt-0.5">QMS</Badge>
                      <span>تصميم وتطوير أنظمة إدارة الوثائق</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Badge variant="outline" className="mt-0.5">Audit</Badge>
                      <span>تنفيذ المراجعات الداخلية وتقييم المطابقة</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Badge variant="outline" className="mt-0.5">KPI</Badge>
                      <span>تطوير مؤشرات الأداء وقياس فعالية العمليات</span>
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold flex items-center gap-2 mb-3">
                    <Code className="h-5 w-5 text-primary" />
                    <span>مهارات البرمجة</span>
                  </h3>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <Badge variant="outline" className="mt-0.5">Frontend</Badge>
                      <span>React, TypeScript, Tailwind CSS</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Badge variant="outline" className="mt-0.5">Backend</Badge>
                      <span>Node.js, Express, SQL, MongoDB</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Badge variant="outline" className="mt-0.5">Tools</Badge>
                      <span>Git, Docker, CI/CD</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Badge variant="outline" className="mt-0.5">Mobile</Badge>
                      <span>React Native, Flutter</span>
                    </li>
                  </ul>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold flex items-center gap-2 mb-3">
                  <Briefcase className="h-5 w-5 text-primary" />
                  <span>عن هذا النظام</span>
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  تم تصميم وتطوير نظام إدارة مستندات الجودة الداخلي بواسطة م. كريم وهيب لتلبية احتياجات المؤسسات 
                  في إدارة وثائق الجودة بكفاءة عالية. يوفر النظام واجهة سهلة الاستخدام لتنظيم وتصنيف وتخزين واسترجاع 
                  المستندات مع إمكانية التحكم في الصلاحيات وإدارة المستخدمين. تم بناء النظام باستخدام أحدث التقنيات 
                  لضمان الأداء العالي والأمان والمرونة.
                </p>
              </div>
            </div>
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              <span>تاريخ الإصدار: {new Date().toLocaleDateString('ar-EG')}</span>
            </div>
            <div>الإصدار 1.0.0</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default About;
