
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AppProvider } from "@/contexts/AppContext";

// Layouts and Pages
import Layout from "./components/Layout";
import Login from "./pages/Login";
import Documents from "./pages/Documents";
import About from "./pages/About";
import Users from "./pages/admin/Users";
import Sectors from "./pages/admin/Sectors";
import Categories from "./pages/admin/Categories";
import Settings from "./pages/admin/Settings";
import DocumentFiles from "./pages/admin/DocumentFiles";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AppProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            {/* Redirect root to documents or login */}
            <Route path="/" element={<Navigate to="/documents" replace />} />

            {/* Auth routes */}
            <Route path="/login" element={<Login />} />

            {/* Protected routes for regular users */}
            <Route element={<Layout requireAuth={true} />}>
              <Route path="/documents" element={<Documents />} />
              <Route path="/about" element={<About />} />
            </Route>

            {/* Protected routes for admin users */}
            <Route element={<Layout requireAuth={true} requireAdmin={true} />}>
              <Route path="/admin/users" element={<Users />} />
              <Route path="/admin/sectors" element={<Sectors />} />
              <Route path="/admin/categories" element={<Categories />} />
              <Route path="/admin/document-files" element={<DocumentFiles />} />
              <Route path="/admin/settings" element={<Settings />} />
            </Route>

            {/* Catch-all route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AppProvider>
  </QueryClientProvider>
);

export default App;
