import React, { useState, useEffect } from 'react';
import { useApp } from '@/contexts/AppContext';
import { indexedDBStorage } from '@/utils/indexedDBStorage';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Search, FileText, Edit, Trash2, Plus, Eye, Download, Upload, FolderX, FileEdit, Shield, Info, ArrowUp, ArrowDown, Loader2 } from 'lucide-react';
import { Progress } from "@/components/ui/progress";
import { Document } from '@/contexts/AppContext';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';

const DocumentFiles: React.FC = () => {
  const {
    documents,
    categories,
    sectors,
    updateDocument,
    deleteDocument,
    isAdmin
  } = useApp();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedSector, setSelectedSector] = useState<string>('');
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);
  
  // حالات لتعديل الملفات
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingDocument, setEditingDocument] = useState<Document | null>(null);
  const [editingFileName, setEditingFileName] = useState('');
  const [editingFileIndex, setEditingFileIndex] = useState<number>(-1);
  const [newFiles, setNewFiles] = useState<File[]>([]);
  
  // حالات لإضافة ملفات جديدة للمستند
  const [isAddFilesDialogOpen, setIsAddFilesDialogOpen] = useState(false);
  const [addingToDocument, setAddingToDocument] = useState<Document | null>(null);
  
  // حالات لتعديل معلومات المستند
  const [isEditDocumentDialogOpen, setIsEditDocumentDialogOpen] = useState(false);
  const [editingDocumentInfo, setEditingDocumentInfo] = useState<Document | null>(null);
  const [editDocumentName, setEditDocumentName] = useState('');
  const [editDocumentCategory, setEditDocumentCategory] = useState('');
  const [editDocumentSector, setEditDocumentSector] = useState('');

  // حالات لعرض معلومات مفصلة عن الملف
  const [isFileInfoDialogOpen, setIsFileInfoDialogOpen] = useState(false);
  const [selectedFileInfo, setSelectedFileInfo] = useState<any>(null);
  
  // حالات التحميل والتقدم لإضافة ملفات جديدة
  const [isUploadingFiles, setIsUploadingFiles] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState('');
  
  // معلومات مساحة التخزين
  const [storageInfo, setStorageInfo] = useState<{ used: number; total: number; percentage: number } | null>(null);

  // حالة نافذة عرض الملف
  const [isFileViewerOpen, setIsFileViewerOpen] = useState(false);
  const [viewingFile, setViewingFile] = useState<any>(null);

  // التأكد من أن المستخدم أدمن وتحميل معلومات التخزين
  useEffect(() => {
    if (!isAdmin) {
      // إعادة توجيه أو عرض رسالة خطأ
      return;
    }
    
    // تحميل معلومات مساحة التخزين
    loadStorageInfo();
  }, [isAdmin]);

  // تحميل معلومات مساحة التخزين
  const loadStorageInfo = async () => {
    try {
      const indexedDBUsage = await indexedDBStorage.getStorageUsage();
      
      let localStorageUsed = 0;
      try {
        for (const key in localStorage) {
          if (localStorage.hasOwnProperty(key)) {
            localStorageUsed += localStorage[key].length + key.length;
          }
        }
      } catch (error) {
        console.error('خطأ في فحص localStorage:', error);
      }
      
      const total = indexedDBUsage.available || 1024 * 1024 * 1024;
      const used = (indexedDBUsage.used || 0) + localStorageUsed;
      const percentage = total > 0 ? (used / total) * 100 : 0;
      
      setStorageInfo({ used, total, percentage });
    } catch (error) {
      console.error('خطأ في تحميل معلومات التخزين:', error);
    }
  };

  // تصفية المستندات
  useEffect(() => {
    let filtered = documents;

    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(doc =>
        doc.name.toLowerCase().includes(term) ||
        doc.files.some(file => file.name.toLowerCase().includes(term))
      );
    }

    if (selectedCategory && selectedCategory !== 'all') {
      filtered = filtered.filter(doc => doc.categoryId === selectedCategory);
    }

    if (selectedSector && selectedSector !== 'all') {
      filtered = filtered.filter(doc => doc.sectorId === selectedSector);
    }

    setFilteredDocuments(filtered);
  }, [documents, searchTerm, selectedCategory, selectedSector]);

  const getCategoryName = (id: string) => {
    return categories.find(c => c.id === id)?.name || 'غير محدد';
  };

  const getSectorName = (id: string) => {
    return sectors.find(s => s.id === id)?.name || 'غير محدد';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // تحويل الملف إلى Base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        if (reader.result) {
          resolve(reader.result as string);
        } else {
          reject(new Error("فشل في قراءة الملف"));
        }
      };
      reader.onerror = error => reject(error);
    });
  };

  // معالجة الملفات تدريجياً لتجنب استهلاك الذاكرة
  const processFilesSequentiallyForAdmin = async (files: File[]): Promise<any[]> => {
    const processedFiles: any[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      try {
        // تحديث حالة التقدم
        const progressPercent = Math.round(((i + 1) / files.length) * 100);
        setUploadProgress(progressPercent);
        setUploadStatus(`معالجة الملف ${i + 1} من ${files.length}: ${file.name}`);
        
        // السماح للواجهة بالتحديث
        await new Promise(resolve => setTimeout(resolve, 10));
        
        // تحويل الملف
        console.log(`معالجة الملف ${i + 1}/${files.length}: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} ميجابايت)`);
        const base64Data = await fileToBase64(file);
        
        // التحقق من صحة البيانات
        if (!base64Data || base64Data.length < 100) {
          throw new Error(`البيانات المحولة للملف ${file.name} غير صالحة`);
        }

        const processedFile = {
          id: `file-${Date.now()}-${i}-${Math.random().toString(36).substring(2, 11)}`,
          name: file.name,
          url: base64Data,
          type: file.type,
          size: file.size,
          dateUploaded: new Date().toISOString()
        };

        processedFiles.push(processedFile);
        console.log(`تم معالجة الملف بنجاح: ${file.name}`);
        
        // تنظيف الذاكرة بين الملفات للملفات الكبيرة
        if (file.size > 5 * 1024 * 1024) { // إذا كان أكبر من 5 ميجابايت
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
      } catch (error) {
        console.error(`خطأ في معالجة الملف ${file.name}:`, error);
        setUploadStatus(`خطأ في معالجة الملف: ${file.name}`);
        throw new Error(`فشل في معالجة الملف ${file.name}: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
      }
    }
    
    return processedFiles;
  };

  // فتح حوار تعديل اسم الملف
  const handleEditFileName = (document: Document, fileIndex: number) => {
    setEditingDocument(document);
    setEditingFileIndex(fileIndex);
    setEditingFileName(document.files[fileIndex].name);
    setIsEditDialogOpen(true);
  };

  // حفظ اسم الملف المعدل
  const handleSaveFileName = () => {
    if (editingDocument && editingFileIndex >= 0 && editingFileName.trim()) {
      const updatedFiles = [...editingDocument.files];
      updatedFiles[editingFileIndex] = {
        ...updatedFiles[editingFileIndex],
        name: editingFileName.trim()
      };

      updateDocument({
        ...editingDocument,
        files: updatedFiles
      });

      setIsEditDialogOpen(false);
      setEditingDocument(null);
      setEditingFileIndex(-1);
      setEditingFileName('');
    }
  };

  // حذف ملف من المستند
  const handleDeleteFile = (document: Document, fileIndex: number) => {
    const updatedFiles = document.files.filter((_, index) => index !== fileIndex);
    
    if (updatedFiles.length === 0) {
      // إذا لم تبق ملفات، احذف المستند كاملاً
      deleteDocument(document.id);
    } else {
      updateDocument({
        ...document,
        files: updatedFiles
      });
    }
  };

  // فتح حوار إضافة ملفات جديدة
  const handleAddFiles = (document: Document) => {
    setAddingToDocument(document);
    setNewFiles([]);
    setIsAddFilesDialogOpen(true);
  };

  // التعامل مع تغيير الملفات المختارة
  const handleNewFilesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setNewFiles(Array.from(e.target.files));
    }
  };

  // إضافة الملفات الجديدة للمستند
  const handleSaveNewFiles = async () => {
    if (!addingToDocument || newFiles.length === 0) return;

    // منع الرفع المتعدد
    if (isUploadingFiles) {
      alert("جارٍ رفع ملفات أخرى، يرجى الانتظار...");
      return;
    }

    try {
      setIsUploadingFiles(true);
      setUploadProgress(0);
      setUploadStatus("بدء عملية رفع الملفات...");
      
      // حساب إجمالي حجم الملفات
      const totalSize = newFiles.reduce((sum, file) => sum + file.size, 0);
      const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);
      console.log(`إضافة ${newFiles.length} ملف بحجم إجمالي ${totalSizeMB} ميجابايت`);
      
      setUploadStatus(`معالجة ${newFiles.length} ملف بحجم إجمالي ${totalSizeMB} ميجابايت...`);

      // استخدام المعالجة التدريجية الجديدة
      const newDocFiles = await processFilesSequentiallyForAdmin(newFiles);
      
      setUploadStatus("حفظ الملفات...");
      
      // إضافة الملفات الجديدة للمستند
      const updatedFiles = [...addingToDocument.files, ...newDocFiles];
      
      updateDocument({
        ...addingToDocument,
        files: updatedFiles
      });

      setUploadStatus("تم الانتهاء بنجاح!");
      setUploadProgress(100);

      // انتظار قليل لإظهار رسالة النجاح ثم إغلاق النافذة
      setTimeout(() => {
        setIsAddFilesDialogOpen(false);
        setAddingToDocument(null);
        setNewFiles([]);
        setUploadProgress(0);
        setUploadStatus('');
      }, 1500);

      // إظهار رسالة نجاح بدون alert
      setUploadStatus(`تم إضافة ${newDocFiles.length} ملف بنجاح للمستند "${addingToDocument.name}"!`);
      
    } catch (error) {
      console.error("خطأ في إضافة الملفات:", error);
      setUploadStatus("حدث خطأ أثناء الرفع");
      alert("حدث خطأ أثناء إضافة الملفات. يرجى المحاولة مرة أخرى: " + (error instanceof Error ? error.message : "خطأ غير معروف"));
    } finally {
      setIsUploadingFiles(false);
    }
  };

  // عرض الملف في نافذة منبثقة
  const handleViewFile = async (file: any) => {
    if (file.type === 'application/pdf' && file.url.startsWith('data:')) {
      try {
        const blob = await (await fetch(file.url)).blob();
        const blobUrl = URL.createObjectURL(blob);
        setViewingFile({ ...file, blobUrl });
      } catch (error) {
        console.error("خطأ في إنشاء رابط للملف:", error);
        // Fallback to original URL if blob creation fails
        setViewingFile(file);
      }
    } else {
      setViewingFile(file);
    }
    setIsFileViewerOpen(true);
  };

  // تحميل الملف
  const handleDownloadFile = (file: any) => {
    try {
      const link = document.createElement('a');
      link.href = file.url;
      link.download = file.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("خطأ في تحميل الملف:", error);
      alert("حدث خطأ أثناء تحميل الملف.");
    }
  };

  // فتح حوار تعديل معلومات المستند
  const handleEditDocument = (document: Document) => {
    setEditingDocumentInfo(document);
    setEditDocumentName(document.name);
    setEditDocumentCategory(document.categoryId);
    setEditDocumentSector(document.sectorId);
    setIsEditDocumentDialogOpen(true);
  };

  // حفظ تعديلات المستند
  const handleSaveDocumentChanges = () => {
    if (editingDocumentInfo && editDocumentName.trim() && editDocumentCategory && editDocumentSector) {
      updateDocument({
        ...editingDocumentInfo,
        name: editDocumentName.trim(),
        categoryId: editDocumentCategory,
        sectorId: editDocumentSector
      });

      setIsEditDocumentDialogOpen(false);
      setEditingDocumentInfo(null);
      setEditDocumentName('');
      setEditDocumentCategory('');
      setEditDocumentSector('');
    }
  };

  // عرض معلومات مفصلة عن الملف
  const handleShowFileInfo = (file: any, document: Document) => {
    setSelectedFileInfo({ ...file, documentName: document.name, documentId: document.id });
    setIsFileInfoDialogOpen(true);
  };

  // نقل الملف للأعلى
  const handleMoveFileUp = (document: Document, fileIndex: number) => {
    if (fileIndex > 0) {
      const updatedFiles = [...document.files];
      [updatedFiles[fileIndex - 1], updatedFiles[fileIndex]] = [updatedFiles[fileIndex], updatedFiles[fileIndex - 1]];
      
      updateDocument({
        ...document,
        files: updatedFiles
      });
    }
  };

  // نقل الملف للأسفل
  const handleMoveFileDown = (document: Document, fileIndex: number) => {
    if (fileIndex < document.files.length - 1) {
      const updatedFiles = [...document.files];
      [updatedFiles[fileIndex], updatedFiles[fileIndex + 1]] = [updatedFiles[fileIndex + 1], updatedFiles[fileIndex]];
      
      updateDocument({
        ...document,
        files: updatedFiles
      });
    }
  };

  if (!isAdmin) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium mb-2">غير مصرح</h3>
        <p className="text-muted-foreground">
          هذه الصفحة متاحة للمديرين فقط.
        </p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">إدارة ملفات المستندات</h1>
          <p className="text-muted-foreground">
            تحكم في الملفات المرفقة بالمستندات - تعديل وحذف وإضافة ملفات جديدة
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={loadStorageInfo}
            title="تحديث معلومات التخزين"
          >
            تحديث التخزين
          </Button>
        </div>
        <div className="flex gap-4 text-sm">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{documents.length}</div>
            <div className="text-muted-foreground">مستند</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {documents.reduce((total, doc) => total + doc.files.length, 0)}
            </div>
            <div className="text-muted-foreground">ملف</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{filteredDocuments.length}</div>
            <div className="text-muted-foreground">نتيجة البحث</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {formatFileSize(documents.reduce((total, doc) => 
                total + doc.files.reduce((fileTotal, file) => fileTotal + file.size, 0), 0
              ))}
            </div>
            <div className="text-muted-foreground">إجمالي الحجم</div>
          </div>
          {storageInfo && (
            <div className="text-center">
              <div className={`text-2xl font-bold ${
                storageInfo.percentage > 80 ? 'text-red-600' : 
                storageInfo.percentage > 60 ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {storageInfo.percentage.toFixed(1)}%
              </div>
              <div className="text-muted-foreground">
                التخزين المستخدم
                <div className="text-xs">
                  {formatFileSize(storageInfo.used)} / {formatFileSize(storageInfo.total)}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* فلاتر البحث */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
            <div className="relative col-span-1 md:col-span-6">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="ابحث في المستندات والملفات..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="col-span-1 md:col-span-3">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="تصفية حسب التصنيف" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">كل التصنيفات</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="col-span-1 md:col-span-3">
              <Select value={selectedSector} onValueChange={setSelectedSector}>
                <SelectTrigger>
                  <SelectValue placeholder="تصفية حسب القطاع" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">كل القطاعات</SelectItem>
                  {sectors.map(sector => (
                    <SelectItem key={sector.id} value={sector.id}>
                      {sector.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* قائمة المستندات */}
      {filteredDocuments.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-muted-foreground/40 mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">لا توجد مستندات</h3>
          <p className="text-muted-foreground">
            لم يتم العثور على أي مستندات تطابق معايير البحث الخاصة بك.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredDocuments.map(document => (
            <Card key={document.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      {document.name}
                    </CardTitle>
                    <CardDescription>
                      <div className="flex flex-wrap gap-4 mt-1">
                        <span>التصنيف: {getCategoryName(document.categoryId)}</span>
                        <span>القطاع: {getSectorName(document.sectorId)}</span>
                        <span>تاريخ الإنشاء: {formatDate(document.dateCreated)}</span>
                        {document.permissions && (
                          <span className="flex items-center gap-1">
                            <Shield className="h-3 w-3" />
                            صلاحيات مخصصة
                          </span>
                        )}
                      </div>
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Badge variant="secondary">
                      {document.files.length} ملف
                    </Badge>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEditDocument(document)}
                    >
                      <FileEdit className="h-4 w-4 mr-1" />
                      تعديل المستند
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleAddFiles(document)}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      إضافة ملفات
                    </Button>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button size="sm" variant="destructive">
                          <FolderX className="h-4 w-4 mr-1" />
                          حذف المستند
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>تأكيد حذف المستند</AlertDialogTitle>
                          <AlertDialogDescription>
                            هل أنت متأكد من حذف المستند "{document.name}" وجميع ملفاته ({document.files.length} ملف)؟
                            <span className="block mt-2 text-destructive font-medium">
                              تحذير: هذا الإجراء لا يمكن التراجع عنه.
                            </span>
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>إلغاء</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => deleteDocument(document.id)}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          >
                            حذف المستند
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ترتيب</TableHead>
                        <TableHead>اسم الملف</TableHead>
                        <TableHead>النوع</TableHead>
                        <TableHead>الحجم</TableHead>
                        <TableHead>تاريخ الرفع</TableHead>
                        <TableHead>الإجراءات</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {document.files.map((file, fileIndex) => (
                        <TableRow key={file.id}>
                          <TableCell>
                            <div className="flex flex-col gap-1">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleMoveFileUp(document, fileIndex)}
                                disabled={fileIndex === 0}
                                title="نقل للأعلى"
                              >
                                <ArrowUp className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleMoveFileDown(document, fileIndex)}
                                disabled={fileIndex === document.files.length - 1}
                                title="نقل للأسفل"
                              >
                                <ArrowDown className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">{file.name}</TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {file.type || 'غير محدد'}
                            </Badge>
                          </TableCell>
                          <TableCell>{formatFileSize(file.size)}</TableCell>
                          <TableCell>{formatDate(file.dateUploaded)}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1 flex-wrap">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleShowFileInfo(file, document)}
                                title="معلومات الملف"
                              >
                                <Info className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleViewFile(file)}
                                title="عرض الملف"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDownloadFile(file)}
                                title="تحميل الملف"
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEditFileName(document, fileIndex)}
                                title="تعديل اسم الملف"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button variant="outline" size="sm">
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>تأكيد الحذف</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      هل أنت متأكد من حذف الملف "{file.name}"؟
                                      {document.files.length === 1 && (
                                        <span className="block mt-2 text-destructive font-medium">
                                          تحذير: هذا هو الملف الوحيد في المستند، سيتم حذف المستند كاملاً.
                                        </span>
                                      )}
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>إلغاء</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => handleDeleteFile(document, fileIndex)}
                                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                    >
                                      حذف
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* حوار تعديل اسم الملف */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تعديل اسم الملف</DialogTitle>
            <DialogDescription>
              قم بتعديل اسم الملف أدناه.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="filename" className="text-right">
                اسم الملف
              </Label>
              <Input
                id="filename"
                value={editingFileName}
                onChange={(e) => setEditingFileName(e.target.value)}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              إلغاء
            </Button>
            <Button onClick={handleSaveFileName}>حفظ</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار إضافة ملفات جديدة */}
      <Dialog open={isAddFilesDialogOpen} onOpenChange={setIsAddFilesDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>إضافة ملفات جديدة</DialogTitle>
            <DialogDescription>
              اختر الملفات التي تريد إضافتها للمستند "{addingToDocument?.name}".
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="newfiles" className="text-right">
                الملفات
              </Label>
              <div className="col-span-3">
                <Input
                  id="newfiles"
                  type="file"
                  multiple
                  onChange={handleNewFilesChange}
                />
                <div className="text-sm text-muted-foreground mt-2">
                  {newFiles.length > 0 ? (
                    <span>تم اختيار {newFiles.length} ملفات</span>
                  ) : (
                    <span>اختر ملفًا واحدًا أو أكثر</span>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          {/* شريط التقدم أثناء الرفع */}
          {isUploadingFiles && (
            <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm font-medium">جارٍ رفع الملفات...</span>
                </div>
                <Progress value={uploadProgress} className="w-full" />
                <p className="text-xs text-muted-foreground mt-2">
                  {uploadProgress}% - {uploadStatus}
                </p>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsAddFilesDialogOpen(false)}
              disabled={isUploadingFiles}
            >
              إلغاء
            </Button>
            <Button 
              onClick={handleSaveNewFiles} 
              disabled={newFiles.length === 0 || isUploadingFiles}
            >
              {isUploadingFiles ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  جارٍ الرفع...
                </>
              ) : (
                'إضافة الملفات'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار تعديل معلومات المستند */}
      <Dialog open={isEditDocumentDialogOpen} onOpenChange={setIsEditDocumentDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تعديل معلومات المستند</DialogTitle>
            <DialogDescription>
              قم بتعديل معلومات المستند "{editingDocumentInfo?.name}".
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-doc-name" className="text-right">
                اسم المستند
              </Label>
              <Input
                id="edit-doc-name"
                value={editDocumentName}
                onChange={(e) => setEditDocumentName(e.target.value)}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-doc-category" className="text-right">
                التصنيف
              </Label>
              <Select value={editDocumentCategory} onValueChange={setEditDocumentCategory}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="اختر التصنيف" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-doc-sector" className="text-right">
                القطاع
              </Label>
              <Select value={editDocumentSector} onValueChange={setEditDocumentSector}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="اختر القطاع" />
                </SelectTrigger>
                <SelectContent>
                  {sectors.map(sector => (
                    <SelectItem key={sector.id} value={sector.id}>
                      {sector.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDocumentDialogOpen(false)}>
              إلغاء
            </Button>
            <Button 
              onClick={handleSaveDocumentChanges}
              disabled={!editDocumentName.trim() || !editDocumentCategory || !editDocumentSector}
            >
              حفظ التغييرات
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* حوار معلومات الملف المفصلة */}
      <Dialog open={isFileInfoDialogOpen} onOpenChange={setIsFileInfoDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>معلومات الملف</DialogTitle>
            <DialogDescription>
              تفاصيل شاملة حول الملف المحدد
            </DialogDescription>
          </DialogHeader>
          {selectedFileInfo && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-3 items-center gap-4">
                <Label className="text-right font-medium">اسم الملف:</Label>
                <div className="col-span-2 text-sm">{selectedFileInfo.name}</div>
              </div>
              <div className="grid grid-cols-3 items-center gap-4">
                <Label className="text-right font-medium">المستند:</Label>
                <div className="col-span-2 text-sm">{selectedFileInfo.documentName}</div>
              </div>
              <div className="grid grid-cols-3 items-center gap-4">
                <Label className="text-right font-medium">نوع الملف:</Label>
                <div className="col-span-2">
                  <Badge variant="outline">{selectedFileInfo.type || 'غير محدد'}</Badge>
                </div>
              </div>
              <div className="grid grid-cols-3 items-center gap-4">
                <Label className="text-right font-medium">الحجم:</Label>
                <div className="col-span-2 text-sm">{formatFileSize(selectedFileInfo.size)}</div>
              </div>
              <div className="grid grid-cols-3 items-center gap-4">
                <Label className="text-right font-medium">تاريخ الرفع:</Label>
                <div className="col-span-2 text-sm">{formatDate(selectedFileInfo.dateUploaded)}</div>
              </div>
              <div className="grid grid-cols-3 items-center gap-4">
                <Label className="text-right font-medium">معرف الملف:</Label>
                <div className="col-span-2 text-xs text-muted-foreground break-all">{selectedFileInfo.id}</div>
              </div>
              <div className="grid grid-cols-3 items-start gap-4">
                <Label className="text-right font-medium">حالة الملف:</Label>
                <div className="col-span-2 text-sm">
                  <Badge variant={selectedFileInfo.url ? "default" : "destructive"}>
                    {selectedFileInfo.url ? "متاح" : "غير متاح"}
                  </Badge>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsFileInfoDialogOpen(false)}>
              إغلاق
            </Button>
            {selectedFileInfo && (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    handleViewFile(selectedFileInfo);
                    setIsFileInfoDialogOpen(false);
                  }}
                >
                  <Eye className="h-4 w-4 mr-1" />
                  عرض
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    handleDownloadFile(selectedFileInfo);
                    setIsFileInfoDialogOpen(false);
                  }}
                >
                  <Download className="h-4 w-4 mr-1" />
                  تحميل
                </Button>
              </div>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* نافذة عرض الملفات */}
      <Dialog open={isFileViewerOpen} onOpenChange={setIsFileViewerOpen}>
        <DialogContent className="max-w-4xl h-[90vh]">
          <DialogHeader>
            <DialogTitle>{viewingFile?.name}</DialogTitle>
            <DialogDescription>
              {viewingFile?.type} - {formatFileSize(viewingFile?.size || 0)}
            </DialogDescription>
          </DialogHeader>
          <div className="h-full py-4">
            {viewingFile && (
              <>
                {viewingFile.type.startsWith('image/') ? (
                  <img src={viewingFile.url} alt={viewingFile.name} className="max-w-full max-h-full mx-auto" />
                ) : viewingFile.type === 'application/pdf' ? (
                  <iframe src={viewingFile.blobUrl || viewingFile.url} className="w-full h-full" frameBorder="0"></iframe>
                ) : (
                  <div className="text-center flex flex-col items-center justify-center h-full">
                    <p className="mb-4">لا يمكن عرض هذا النوع من الملفات مباشرة.</p>
                    <Button onClick={() => handleDownloadFile(viewingFile)}>
                      <Download className="h-4 w-4 mr-2" />
                      تحميل الملف
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsFileViewerOpen(false)}>
              إغلاق
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DocumentFiles;