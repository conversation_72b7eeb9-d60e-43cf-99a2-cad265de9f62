
import React, { useState } from 'react';
import { useApp } from '@/contexts/AppContext';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Plus, Trash, Edit } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Sector } from '@/contexts/AppContext';

const Sectors: React.FC = () => {
  const { sectors, addSector, updateSector, deleteSector, documents } = useApp();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [newSectorName, setNewSectorName] = useState('');
  const [selectedSector, setSelectedSector] = useState<Sector | null>(null);

  const handleAddSector = () => {
    if (!newSectorName) return;
    
    addSector(newSectorName);
    setNewSectorName('');
    setIsAddDialogOpen(false);
  };

  const handleEditSector = () => {
    if (!selectedSector || !newSectorName) return;
    
    updateSector(selectedSector.id, newSectorName);
    setSelectedSector(null);
    setNewSectorName('');
    setIsEditDialogOpen(false);
  };

  const handleEdit = (sector: Sector) => {
    setSelectedSector(sector);
    setNewSectorName(sector.name);
    setIsEditDialogOpen(true);
  };

  const handleDeleteSector = (sectorId: string) => {
    // Check if there are any documents using this sector
    const hasDocuments = documents.some(doc => doc.sectorId === sectorId);
    
    if (hasDocuments) {
      alert('لا يمكن حذف هذا القطاع لأنه مستخدم في مستندات');
      return;
    }
    
    if (confirm('هل أنت متأكد من حذف هذا القطاع؟')) {
      deleteSector(sectorId);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">إدارة القطاعات</h1>
        
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" /> إضافة قطاع جديد
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>إضافة قطاع جديد</DialogTitle>
              <DialogDescription>
                أدخل اسم القطاع الجديد.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  اسم القطاع
                </Label>
                <Input
                  id="name"
                  value={newSectorName}
                  onChange={(e) => setNewSectorName(e.target.value)}
                  className="col-span-3"
                  placeholder="أدخل اسم القطاع"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                إلغاء
              </Button>
              <Button onClick={handleAddSector}>إضافة</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        
        {/* Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>تعديل القطاع</DialogTitle>
              <DialogDescription>
                تعديل اسم القطاع.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-name" className="text-right">
                  اسم القطاع
                </Label>
                <Input
                  id="edit-name"
                  value={newSectorName}
                  onChange={(e) => setNewSectorName(e.target.value)}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                إلغاء
              </Button>
              <Button onClick={handleEditSector}>حفظ التغييرات</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      
      <Card className="p-6 mb-6">
        <p className="text-muted-foreground">
          القطاعات هي التقسيمات الرئيسية للمؤسسة التي يتم تصنيف المستندات على أساسها، مثل قطاع الإنتاج، قطاع الجودة، وغيرها.
        </p>
      </Card>
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>اسم القطاع</TableHead>
              <TableHead>عدد المستندات</TableHead>
              <TableHead>الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sectors.map(sector => {
              const sectorDocumentsCount = documents.filter(doc => doc.sectorId === sector.id).length;
              
              return (
                <TableRow key={sector.id}>
                  <TableCell className="font-medium">{sector.name}</TableCell>
                  <TableCell>{sectorDocumentsCount}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="icon" onClick={() => handleEdit(sector)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleDeleteSector(sector.id)}
                        disabled={sectorDocumentsCount > 0}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

// Simple Label component for the form
const Label: React.FC<{
  htmlFor: string;
  className?: string;
  children: React.ReactNode;
}> = ({ htmlFor, className, children }) => (
  <label
    htmlFor={htmlFor}
    className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${className}`}
  >
    {children}
  </label>
);

export default Sectors;
