@echo off
cls
echo ========================================
echo    System Check for QMS Flow
echo ========================================
echo.

echo Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found!
    echo Please install Node.js from: https://nodejs.org
    goto :end
) else (
    echo OK: Node.js is installed
)

echo.
echo Checking npm...
npm --version
if %errorlevel% neq 0 (
    echo ERROR: npm not found!
    goto :end
) else (
    echo OK: npm is available
)

echo.
echo Checking project files...
if exist "package.json" (
    echo OK: package.json found
) else (
    echo ERROR: package.json not found!
    echo Make sure you're in the correct project folder
    goto :end
)

if exist "node_modules" (
    echo OK: node_modules folder exists
) else (
    echo WARNING: node_modules not found
    echo Run 'npm install' to install dependencies
)

echo.
echo Checking network configuration...
ipconfig | findstr /i "IPv4"

echo.
echo ========================================
echo System check complete!
echo ========================================

:end
echo.
pause
