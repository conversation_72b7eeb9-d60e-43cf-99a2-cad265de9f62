
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useApp } from '@/contexts/AppContext';
import { cn } from '@/lib/utils';
import {
  FileText,
  Settings,
  Users,
  FolderOpen,
  Tag,
  Info,
  Files
} from 'lucide-react';

const Sidebar: React.FC = () => {
  const { isAdmin } = useApp();
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <aside className="w-64 bg-sidebar text-sidebar-foreground flex-shrink-0 border-r border-sidebar-border">
      <div className="p-6">
        <h2 className="text-xl font-bold text-sidebar-foreground">QMS Flow</h2>
        <p className="text-sm opacity-80 mt-1">نظام إدارة مستندات الجودة</p>
      </div>

      <nav className="mt-6">
        <div className="px-3 py-2">
          <p className="text-xs uppercase tracking-wider opacity-70 px-3 mb-2">
            التصفح الرئيسي
          </p>

          <div className="space-y-1">
            <Link
              to="/documents"
              className={cn(
                "flex items-center gap-3 px-3 py-2 rounded-md transition-colors",
                isActive("/documents")
                  ? "bg-sidebar-accent text-sidebar-accent-foreground font-medium"
                  : "hover:bg-sidebar-accent/50 text-sidebar-foreground"
              )}
            >
              <FileText className="h-5 w-5" />
              <span>المستندات</span>
            </Link>

            <Link
              to="/about"
              className={cn(
                "flex items-center gap-3 px-3 py-2 rounded-md transition-colors",
                isActive("/about")
                  ? "bg-sidebar-accent text-sidebar-accent-foreground font-medium"
                  : "hover:bg-sidebar-accent/50 text-sidebar-foreground"
              )}
            >
              <Info className="h-5 w-5" />
              <span>حول البرنامج</span>
            </Link>
          </div>
        </div>

        {isAdmin && (
          <div className="px-3 py-2 mt-6">
            <p className="text-xs uppercase tracking-wider opacity-70 px-3 mb-2">
              الإدارة
            </p>

            <div className="space-y-1">
              <Link
                to="/admin/users"
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-md transition-colors",
                  isActive("/admin/users")
                    ? "bg-sidebar-accent text-sidebar-accent-foreground font-medium"
                    : "hover:bg-sidebar-accent/50 text-sidebar-foreground"
                )}
              >
                <Users className="h-5 w-5" />
                <span>المستخدمون</span>
              </Link>

              <Link
                to="/admin/sectors"
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-md transition-colors",
                  isActive("/admin/sectors")
                    ? "bg-sidebar-accent text-sidebar-accent-foreground font-medium"
                    : "hover:bg-sidebar-accent/50 text-sidebar-foreground"
                )}
              >
                <FolderOpen className="h-5 w-5" />
                <span>القطاعات</span>
              </Link>

              <Link
                to="/admin/categories"
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-md transition-colors",
                  isActive("/admin/categories")
                    ? "bg-sidebar-accent text-sidebar-accent-foreground font-medium"
                    : "hover:bg-sidebar-accent/50 text-sidebar-foreground"
                )}
              >
                <Tag className="h-5 w-5" />
                <span>التصنيفات</span>
              </Link>

              <Link
                to="/admin/document-files"
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-md transition-colors",
                  isActive("/admin/document-files")
                    ? "bg-sidebar-accent text-sidebar-accent-foreground font-medium"
                    : "hover:bg-sidebar-accent/50 text-sidebar-foreground"
                )}
              >
                <Files className="h-5 w-5" />
                <span>إدارة الملفات</span>
              </Link>

              <Link
                to="/admin/settings"
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-md transition-colors",
                  isActive("/admin/settings")
                    ? "bg-sidebar-accent text-sidebar-accent-foreground font-medium"
                    : "hover:bg-sidebar-accent/50 text-sidebar-foreground"
                )}
              >
                <Settings className="h-5 w-5" />
                <span>الإعدادات</span>
              </Link>
            </div>
          </div>
        )}
      </nav>
    </aside>
  );
};

export default Sidebar;
