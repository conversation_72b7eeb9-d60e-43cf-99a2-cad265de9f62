========================================
   كيفية تشغيل QMS Flow على الشبكة المحلية
========================================

الطريقة الأولى: الاسكريبت البسيط (الأفضل)
===========================================
1. انقر نقراً مزدوجاً على: simple-network.bat
2. ستظهر نافذة سوداء مع معلومات الخادم
3. انتظر حتى يظهر "ready in" أو "Local:" في النافذة
4. اترك النافذة مفتوحة (لا تغلقها)

الطريقة الثانية: الأوامر اليدوية
===============================
1. افتح Command Prompt في مجلد المشروع
2. اكتب: npm run dev:network
3. اضغط Enter

معرفة عنوان IP الخاص بك
========================
1. انقر نقراً مزدوجاً على: get-ip.bat
2. ستظهر قائمة بعناوين IP
3. ابحث عن السطر الذي يحتوي على "192.168" أو "10.0"
4. هذا هو عنوان IP المحلي

الوصول للتطبيق
==============
من نفس الجهاز:
http://localhost:5173

من أجهزة أخرى في الشبكة:
http://[عنوان-IP]:5173

مثال:
http://*************:5173

استكشاف الأخطاء
===============
إذا لم يعمل الاسكريبت:
1. تأكد من تثبيت Node.js
2. افتح Command Prompt كمدير
3. انتقل لمجلد المشروع
4. اكتب: npm install
5. ثم: npm run dev:network

إذا لم تستطع الوصول من أجهزة أخرى:
1. تأكد من أن جدار الحماية لا يحجب المنفذ 5173
2. تأكد من أن جميع الأجهزة في نفس الشبكة
3. جرب إيقاف جدار الحماية مؤقتاً للاختبار

ملاحظات مهمة
=============
- اترك نافذة Command Prompt مفتوحة أثناء الاستخدام
- لإيقاف الخادم: اضغط Ctrl+C في النافذة
- لا تغلق النافذة إلا إذا كنت تريد إيقاف الخادم

========================================
