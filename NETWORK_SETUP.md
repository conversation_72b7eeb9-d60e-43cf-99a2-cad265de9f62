# 🌐 تشغيل QMS Flow على الشبكة المحلية

## 📋 نظرة عامة

هذا الدليل يوضح كيفية تشغيل تطبيق QMS Flow على الشبكة المحلية بحيث يمكن للأجهزة الأخرى في نفس الشبكة الوصول إليه.

## 🚀 طرق التشغيل

### 1. استخدام الاسكريبت التلقائي (الطريقة المفضلة)

#### لنظام Windows:
```bash
# انقر نقراً مزدوجاً على الملف أو شغله من Command Prompt
start-network.bat
```

#### لنظام Linux/Mac:
```bash
# اجعل الملف قابل للتنفيذ أولاً
chmod +x start-network.sh

# ثم شغله
./start-network.sh
```

### 2. التشغيل اليدوي

```bash
# تثبيت التبعيات (إذا لم تكن مثبتة)
npm install

# تشغيل التطبيق على الشبكة
npm run dev -- --host 0.0.0.0 --port 5173
```

## 🔧 إعدادات إضافية

### تخصيص المنفذ

إذا كنت تريد استخدام منفذ مختلف:

```bash
# مثال: استخدام المنفذ 3000
npm run dev -- --host 0.0.0.0 --port 3000
```

### إضافة إعدادات دائمة في package.json

يمكنك إضافة اسكريبت جديد في `package.json`:

```json
{
  "scripts": {
    "dev": "vite",
    "dev:network": "vite --host 0.0.0.0 --port 5173",
    "build": "tsc && vite build",
    "preview": "vite preview"
  }
}
```

ثم تشغيله بـ:
```bash
npm run dev:network
```

## 🌐 الوصول للتطبيق

بعد تشغيل الاسكريبت، ستحصل على عنوانين:

### الوصول المحلي:
```
http://localhost:5173
```

### الوصول من الشبكة:
```
http://[عنوان-IP-المحلي]:5173
```

مثال:
```
http://*************:5173
```

## 🔍 كيفية معرفة عنوان IP المحلي

### Windows:
```cmd
ipconfig
```
ابحث عن "IPv4 Address" في قسم الشبكة النشطة

### Linux/Mac:
```bash
# الطريقة الأولى
hostname -I

# الطريقة الثانية
ifconfig | grep "inet "

# الطريقة الثالثة
ip addr show
```

## 🛡️ إعدادات جدار الحماية

### Windows Defender:

1. افتح "Windows Security"
2. انتقل إلى "Firewall & network protection"
3. انقر على "Allow an app through firewall"
4. أضف Node.js أو السماح للمنفذ 5173

### Linux (UFW):
```bash
sudo ufw allow 5173
```

### macOS:
```bash
# عادة لا يحتاج إعدادات إضافية
# لكن يمكنك التحقق من System Preferences > Security & Privacy > Firewall
```

## 📱 الوصول من الأجهزة المحمولة

1. تأكد أن الجهاز المحمول متصل بنفس شبكة WiFi
2. افتح المتصفح في الجهاز المحمول
3. اكتب عنوان IP مع المنفذ:
   ```
   http://*************:5173
   ```

## 🔧 استكشاف الأخطاء

### المشكلة: لا يمكن الوصول من أجهزة أخرى

**الحلول:**
1. تأكد من تشغيل التطبيق بـ `--host 0.0.0.0`
2. تحقق من إعدادات جدار الحماية
3. تأكد أن جميع الأجهزة في نفس الشبكة
4. جرب إيقاف جدار الحماية مؤقتاً للاختبار

### المشكلة: المنفذ مستخدم

**الحل:**
```bash
# استخدم منفذ مختلف
npm run dev -- --host 0.0.0.0 --port 3000
```

### المشكلة: بطء في التحميل

**الحلول:**
1. تأكد من قوة إشارة WiFi
2. أغلق التطبيقات الأخرى التي تستخدم الشبكة
3. جرب منفذ مختلف

## 📊 مراقبة الأداء

### عرض الاتصالات النشطة:

**Windows:**
```cmd
netstat -an | findstr :5173
```

**Linux/Mac:**
```bash
netstat -an | grep :5173
# أو
lsof -i :5173
```

## 🔒 اعتبارات الأمان

⚠️ **تحذير مهم:**
- هذا الإعداد مخصص للشبكات المحلية الآمنة فقط
- لا تستخدمه على شبكات عامة أو غير آمنة
- لا تعرض التطبيق على الإنترنت بدون إعدادات أمان إضافية

### للاستخدام الآمن:
1. استخدم فقط في الشبكات الموثوقة
2. أغلق التطبيق عند عدم الحاجة
3. راقب الاتصالات النشطة
4. استخدم VPN إذا كنت تحتاج الوصول من خارج الشبكة المحلية

## 📝 ملاحظات إضافية

- التطبيق سيعمل على جميع واجهات الشبكة (`0.0.0.0`)
- المنفذ الافتراضي هو `5173`
- يمكن الوصول من أي جهاز في نفس الشبكة
- التطبيق سيعرض عنوان IP المحلي تلقائياً عند التشغيل

## 🎯 الخلاصة

✅ **استخدم `start-network.bat` في Windows**  
✅ **استخدم `start-network.sh` في Linux/Mac**  
✅ **تأكد من إعدادات جدار الحماية**  
✅ **اختبر الوصول من أجهزة مختلفة**  
✅ **راقب الأمان والأداء**  

🎉 **الآن يمكن لجميع الأجهزة في الشبكة المحلية الوصول لتطبيق QMS Flow!**
