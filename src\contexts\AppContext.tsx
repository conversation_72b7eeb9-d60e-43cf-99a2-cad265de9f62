import React, { createContext, useState, useContext, useEffect } from 'react';
import { indexedDBStorage } from '@/utils/indexedDBStorage';

export interface User {
  id: string;
  username: string;
  password: string; // إضافة حقل كلمة المرور
  role: 'admin' | 'user';
  sectors: string[];
  permissions: {
    view: string[];
    download: string[];
    manage: string[];
  };
  fullName?: string; // إضافة الاسم الكامل
  phoneNumber?: string; // إضافة رقم الهاتف
}

export interface Sector {
  id: string;
  name: string;
}

export interface Category {
  id: string;
  name: string;
}

export interface Document {
  id: string;
  name: string;
  categoryId: string;
  sectorId: string;
  files: Array<{
    id: string;
    name: string;
    url: string; // This would be a local storage reference in the actual implementation
    type: string;
    size: number;
    dateUploaded: string;
  }>;
  dateCreated: string;
  createdBy: string;
  permissions?: {
    view: string[];
    download: string[];
  };
}

interface AppContextType {
  currentUser: User | null;
  isAuthenticated: boolean;
  isAdmin: boolean;
  sectors: Sector[];
  categories: Category[];
  documents: Document[];
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  addSector: (name: string) => void;
  updateSector: (id: string, name: string) => void;
  deleteSector: (id: string) => void;
  addCategory: (name: string) => void;
  updateCategory: (id: string, name: string) => void;
  deleteCategory: (id: string) => void;
  addDocument: (document: Omit<Document, 'id' | 'dateCreated' | 'createdBy'>) => Promise<void>;
  updateDocument: (document: Partial<Document> & { id: string }) => void;
  deleteDocument: (id: string) => void;
  getUserPermissions: (userId: string) => User['permissions'];
  updateUserPermissions: (userId: string, permissions: User['permissions']) => void;
  users: User[];
  addUser: (user: Omit<User, 'id'>) => void;
  updateUser: (id: string, user: Partial<Omit<User, 'id'>>) => void;
  deleteUser: (id: string) => void;
}

// Create initial mock data
const mockUsers: User[] = [
  {
    id: '1',
    username: 'admin',
    password: 'admin123', // كلمة مرور المدير
    role: 'admin',
    sectors: ['all'],
    permissions: {
      view: ['all'],
      download: ['all'],
      manage: ['all']
    }
  },
  {
    id: '2',
    username: 'user',
    password: 'user123', // كلمة مرور المستخدم العادي
    role: 'user',
    sectors: ['1'],
    permissions: {
      view: ['1', '2'],
      download: ['1'],
      manage: []
    }
  },
  {
    id: '3',
    username: 'karim',
    password: 'karim123',
    role: 'admin',
    fullName: 'كريم وهيب',
    phoneNumber: '01159296333',
    sectors: ['all'],
    permissions: {
      view: ['all'],
      download: ['all'],
      manage: ['all']
    }
  }
];

const mockSectors: Sector[] = [
  { id: '1', name: 'قطاع الإنتاج' },
  { id: '2', name: 'قطاع الجودة' },
  { id: '3', name: 'قطاع الإدارة' }
];

const mockCategories: Category[] = [
  { id: '1', name: 'إجراءات العمل' },
  { id: '2', name: 'تعليمات العمل' },
  { id: '3', name: 'خطط الفحص' },
  { id: '4', name: 'خطط الجودة' }
];

const mockDocuments: Document[] = [
  {
    id: '1',
    name: 'إجراء رقم 1',
    categoryId: '1',
    sectorId: '1',
    files: [
      {
        id: 'f1',
        name: 'file1.pdf',
        // استخدام صورة Base64 بسيطة كمثال
        url: 'data:application/pdf;base64,JVBERi0xLjcKJeLjz9MKNSAwIG9iago8PCAvVHlwZSAvWE9iamVjdCAvU3VidHlwZSAvSW1hZ2UgL1dpZHRoIDEyMDAgL0hlaWdodCA4MDAgL0JpdHNQZXJDb21wb25lbnQgOCAvQ29sb3JTcGFjZSAvRGV2aWNlUkdCIC9GaWx0ZXIgL0RDVERlY29kZSAvTGVuZ3RoIDEyMzQ1ID4+CnN0cmVhbQpRRUQgRXhhbXBsZSBQREYgRmlsZQplbmRzdHJlYW0KZW5kb2JqCjYgMCBvYmoKPDwgL1R5cGUgL1hPYmplY3QgL1N1YnR5cGUgL0ltYWdlIC9XaWR0aCAxMjAwIC9IZWlnaHQgODAwIC9CaXRzUGVyQ29tcG9uZW50IDggL0NvbG9yU3BhY2UgL0RldmljZVJHQiAvRmlsdGVyIC9EQ1REZWNvZGUgL0xlbmd0aCAxMjM0NSA+PgpzdHJlYW0KUUVEIEZpbGUgQ29udGVudApRTVMgRG9jdW1lbnQKZW5kc3RyZWFtCmVuZG9iagp4cmVmCjAgNwowMDAwMDAwMDAwIDY1NTM1IGYNCjAwMDAwMDAwMTAgMDAwMDAgbg0KMDAwMDAwMDA3OSAwMDAwMCBuDQowMDAwMDAwMTczIDAwMDAwIG4NCjAwMDAwMDAzMDEgMDAwMDAgbg0KMDAwMDAwMDM4MCAwMDAwMCBuDQowMDAwMDEyNzMxIDAwMDAwIG4NCnRyYWlsZXIKPDwgL1NpemUgNyAvUm9vdCAxIDAgUiAvSW5mbyAyIDAgUiA+PgpzdGFydHhyZWYKMjUwODIKJSVFT0YK',
        type: 'application/pdf',
        size: 1024,
        dateUploaded: '2025-05-01T00:00:00.000Z'
      }
    ],
    dateCreated: '2025-05-01T00:00:00.000Z',
    createdBy: '1',
    permissions: {
      view: ['all'],
      download: ['all']
    }
  },
  {
    id: '2',
    name: 'تعليمة رقم 12',
    categoryId: '2',
    sectorId: '2',
    files: [
      {
        id: 'f2',
        name: 'file2.pdf',
        // استخدام صورة Base64 بسيطة كمثال
        url: 'data:application/pdf;base64,JVBERi0xLjcKJeLjz9MKNSAwIG9iago8PCAvVHlwZSAvWE9iamVjdCAvU3VidHlwZSAvSW1hZ2UgL1dpZHRoIDEyMDAgL0hlaWdodCA4MDAgL0JpdHNQZXJDb21wb25lbnQgOCAvQ29sb3JTcGFjZSAvRGV2aWNlUkdCIC9GaWx0ZXIgL0RDVERlY29kZSAvTGVuZ3RoIDEyMzQ1ID4+CnN0cmVhbQpRRUQgRXhhbXBsZSBQREYgRmlsZQplbmRzdHJlYW0KZW5kb2JqCjYgMCBvYmoKPDwgL1R5cGUgL1hPYmplY3QgL1N1YnR5cGUgL0ltYWdlIC9XaWR0aCAxMjAwIC9IZWlnaHQgODAwIC9CaXRzUGVyQ29tcG9uZW50IDggL0NvbG9yU3BhY2UgL0RldmljZVJHQiAvRmlsdGVyIC9EQ1REZWNvZGUgL0xlbmd0aCAxMjM0NSA+PgpzdHJlYW0KUUVEIEZpbGUgQ29udGVudApRTVMgRG9jdW1lbnQKZW5kc3RyZWFtCmVuZG9iagp4cmVmCjAgNwowMDAwMDAwMDAwIDY1NTM1IGYNCjAwMDAwMDAwMTAgMDAwMDAgbg0KMDAwMDAwMDA3OSAwMDAwMCBuDQowMDAwMDAwMTczIDAwMDAwIG4NCjAwMDAwMDAzMDEgMDAwMDAgbg0KMDAwMDAwMDM4MCAwMDAwMCBuDQowMDAwMDEyNzMxIDAwMDAwIG4NCnRyYWlsZXIKPDwgL1NpemUgNyAvUm9vdCAxIDAgUiAvSW5mbyAyIDAgUiA+PgpzdGFydHhyZWYKMjUwODIKJSVFT0YK',
        type: 'application/pdf',
        size: 2048,
        dateUploaded: '2025-05-02T00:00:00.000Z'
      },
      {
        id: 'f3',
        name: 'file3.pdf',
        // استخدام صورة Base64 بسيطة كمثال
        url: 'data:application/pdf;base64,JVBERi0xLjcKJeLjz9MKNSAwIG9iago8PCAvVHlwZSAvWE9iamVjdCAvU3VidHlwZSAvSW1hZ2UgL1dpZHRoIDEyMDAgL0hlaWdodCA4MDAgL0JpdHNQZXJDb21wb25lbnQgOCAvQ29sb3JTcGFjZSAvRGV2aWNlUkdCIC9GaWx0ZXIgL0RDVERlY29kZSAvTGVuZ3RoIDEyMzQ1ID4+CnN0cmVhbQpRRUQgRXhhbXBsZSBQREYgRmlsZQplbmRzdHJlYW0KZW5kb2JqCjYgMCBvYmoKPDwgL1R5cGUgL1hPYmplY3QgL1N1YnR5cGUgL0ltYWdlIC9XaWR0aCAxMjAwIC9IZWlnaHQgODAwIC9CaXRzUGVyQ29tcG9uZW50IDggL0NvbG9yU3BhY2UgL0RldmljZVJHQiAvRmlsdGVyIC9EQ1REZWNvZGUgL0xlbmd0aCAxMjM0NSA+PgpzdHJlYW0KUUVEIEZpbGUgQ29udGVudApRTVMgRG9jdW1lbnQKZW5kc3RyZWFtCmVuZG9iagp4cmVmCjAgNwowMDAwMDAwMDAwIDY1NTM1IGYNCjAwMDAwMDAwMTAgMDAwMDAgbg0KMDAwMDAwMDA3OSAwMDAwMCBuDQowMDAwMDAwMTczIDAwMDAwIG4NCjAwMDAwMDAzMDEgMDAwMDAgbg0KMDAwMDAwMDM4MCAwMDAwMCBuDQowMDAwMDEyNzMxIDAwMDAwIG4NCnRyYWlsZXIKPDwgL1NpemUgNyAvUm9vdCAxIDAgUiAvSW5mbyAyIDAgUiA+PgpzdGFydHhyZWYKMjUwODIKJSVFT0YK',
        type: 'application/pdf',
        size: 3072,
        dateUploaded: '2025-05-02T00:00:00.000Z'
      }
    ],
    dateCreated: '2025-05-02T00:00:00.000Z',
    createdBy: '1',
    permissions: {
      view: ['all'],
      download: ['2']
    }
  }
];

// Initialize localStorage with mock data if it doesn't exist
const initializeLocalStorage = () => {
  if (!localStorage.getItem('qms_users')) {
    localStorage.setItem('qms_users', JSON.stringify(mockUsers));
  }
  if (!localStorage.getItem('qms_sectors')) {
    localStorage.setItem('qms_sectors', JSON.stringify(mockSectors));
  }
  if (!localStorage.getItem('qms_categories')) {
    localStorage.setItem('qms_categories', JSON.stringify(mockCategories));
  }
  if (!localStorage.getItem('qms_documents')) {
    localStorage.setItem('qms_documents', JSON.stringify(mockDocuments));
  }
};

// Create context
export const AppContext = createContext<AppContextType | null>(null);

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [sectors, setSectors] = useState<Sector[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [users, setUsers] = useState<User[]>([]);

  // Initialize data from localStorage and IndexedDB
  useEffect(() => {
    const initializeData = async () => {
      try {
        // تهيئة IndexedDB
        await indexedDBStorage.init();
        console.log('تم تهيئة IndexedDB بنجاح');

        // تهيئة localStorage للبيانات الأساسية
        initializeLocalStorage();

        // تحميل البيانات الأساسية من localStorage (صغيرة الحجم)
        const storedSectors = localStorage.getItem('qms_sectors');
        const storedCategories = localStorage.getItem('qms_categories');
        const storedUsers = localStorage.getItem('qms_users');

        if (storedSectors) setSectors(JSON.parse(storedSectors));
        if (storedCategories) setCategories(JSON.parse(storedCategories));
        if (storedUsers) setUsers(JSON.parse(storedUsers));

        // تحميل المستندات من IndexedDB (كبيرة الحجم)
        try {
          const storedDocuments = await indexedDBStorage.loadDocuments();
          if (storedDocuments && storedDocuments.length > 0) {
            setDocuments(storedDocuments);
            console.log(`تم تحميل ${storedDocuments.length} مستند من IndexedDB`);
          } else {
            // إذا لم توجد مستندات في IndexedDB، تحقق من localStorage
            const localStorageDocuments = localStorage.getItem('qms_documents');
            if (localStorageDocuments) {
              const parsedDocs = JSON.parse(localStorageDocuments);
              setDocuments(parsedDocs);
              // نقل البيانات إلى IndexedDB
              await indexedDBStorage.saveDocuments(parsedDocs);
              console.log('تم نقل المستندات من localStorage إلى IndexedDB');
            }
          }
        } catch (error) {
          console.error('خطأ في تحميل المستندات من IndexedDB:', error);
          // fallback إلى localStorage
          const localStorageDocuments = localStorage.getItem('qms_documents');
          if (localStorageDocuments) {
            setDocuments(JSON.parse(localStorageDocuments));
          }
        }

        // Check if user is logged in
        const storedUser = localStorage.getItem('qms_currentUser');
        if (storedUser) {
          setCurrentUser(JSON.parse(storedUser));
        }

      } catch (error) {
        console.error('خطأ في تهيئة التطبيق:', error);
        // fallback إلى localStorage فقط
        const storedSectors = localStorage.getItem('qms_sectors');
        const storedCategories = localStorage.getItem('qms_categories');
        const storedDocuments = localStorage.getItem('qms_documents');
        const storedUsers = localStorage.getItem('qms_users');

        if (storedSectors) setSectors(JSON.parse(storedSectors));
        if (storedCategories) setCategories(JSON.parse(storedCategories));
        if (storedDocuments) setDocuments(JSON.parse(storedDocuments));
        if (storedUsers) setUsers(JSON.parse(storedUsers));
      }
    };

    initializeData();
  }, []);

  // Save data to localStorage whenever it changes
  useEffect(() => {
    if (sectors.length > 0) localStorage.setItem('qms_sectors', JSON.stringify(sectors));
  }, [sectors]);

  useEffect(() => {
    if (categories.length > 0) localStorage.setItem('qms_categories', JSON.stringify(categories));
  }, [categories]);

  useEffect(() => {
    if (documents.length > 0) {
      const saveDocuments = async () => {
        try {
          // حفظ في IndexedDB (للملفات الكبيرة)
          await indexedDBStorage.saveDocuments(documents);
          console.log(`تم حفظ ${documents.length} مستند في IndexedDB`);
          
          // أيضاً حفظ نسخة مصغرة في localStorage كـ backup (بدون الملفات)
          const documentsWithoutFiles = documents.map(doc => ({
            ...doc,
            files: doc.files.map(f => ({ id: f.id, name: f.name, type: f.type, size: f.size, dateUploaded: f.dateUploaded }))
          }));
          
          try {
            localStorage.setItem('qms_documents_backup', JSON.stringify(documentsWithoutFiles));
          } catch (localStorageError) {
            console.warn('فشل حفظ backup في localStorage:', localStorageError);
          }
          
        } catch (error) {
          console.error('خطأ في الحفظ التلقائي للمستندات:', error);
          // fallback إلى localStorage إذا فشل IndexedDB
          try {
            localStorage.setItem('qms_documents', JSON.stringify(documents));
            console.log('تم الحفظ في localStorage كـ fallback');
          } catch (localStorageError) {
            console.error('فشل الحفظ في localStorage أيضاً:', localStorageError);
          }
        }
      };
      
      saveDocuments();
    }
  }, [documents]);

  useEffect(() => {
    if (users.length > 0) localStorage.setItem('qms_users', JSON.stringify(users));
  }, [users]);

  // Authentication functions
  const login = async (username: string, password: string): Promise<boolean> => {
    // تحقق من اسم المستخدم وكلمة المرور
    const user = users.find(u => u.username === username && u.password === password);

    if (user) {
      setCurrentUser(user);
      localStorage.setItem('qms_currentUser', JSON.stringify(user));
      return true;
    }
    return false;
  };

  const logout = () => {
    setCurrentUser(null);
    localStorage.removeItem('qms_currentUser');
  };

  // Sector management
  const addSector = (name: string) => {
    const newSector = {
      id: `${Date.now()}`,
      name
    };
    setSectors([...sectors, newSector]);
  };

  const updateSector = (id: string, name: string) => {
    setSectors(sectors.map(sector =>
      sector.id === id ? { ...sector, name } : sector
    ));
  };

  const deleteSector = (id: string) => {
    setSectors(sectors.filter(sector => sector.id !== id));
  };

  // Category management
  const addCategory = (name: string) => {
    const newCategory = {
      id: `${Date.now()}`,
      name
    };
    setCategories([...categories, newCategory]);
  };

  const updateCategory = (id: string, name: string) => {
    setCategories(categories.map(category =>
      category.id === id ? { ...category, name } : category
    ));
  };

  const deleteCategory = (id: string) => {
    setCategories(categories.filter(category => category.id !== id));
  };

  // Document management
  const addDocument = async (document: Omit<Document, 'id' | 'dateCreated' | 'createdBy'>) => {
    try {
      const newDocument: Document = {
        ...document,
        id: `${Date.now()}`,
        dateCreated: new Date().toISOString(),
        createdBy: currentUser?.id || ''
      };
      
      console.log(`إضافة مستند جديد: ${newDocument.name} مع ${newDocument.files.length} ملف`);
      
      // حساب حجم الملفات
      const totalFileSize = newDocument.files.reduce((sum, file) => sum + (file.size || 0), 0);
      const totalSizeMB = (totalFileSize / 1024 / 1024).toFixed(2);
      console.log(`إجمالي حجم الملفات: ${totalSizeMB} ميجابايت`);
      
      const updatedDocuments = [...documents, newDocument];
      
      // محاولة حفظ المستند في IndexedDB (يدعم مساحات كبيرة)
      try {
        await indexedDBStorage.saveDocuments(updatedDocuments);
        console.log('تم حفظ المستند بنجاح في IndexedDB');
        
        // حفظ نسخة مصغرة في localStorage كـ backup
        const documentsWithoutFiles = updatedDocuments.map(doc => ({
          ...doc,
          files: doc.files.map(f => ({ id: f.id, name: f.name, type: f.type, size: f.size, dateUploaded: f.dateUploaded }))
        }));
        
        try {
          localStorage.setItem('qms_documents_backup', JSON.stringify(documentsWithoutFiles));
        } catch (localStorageError) {
          console.warn('فشل حفظ backup في localStorage:', localStorageError);
        }
        
      } catch (indexedDBError) {
        console.error('خطأ في حفظ المستند في IndexedDB:', indexedDBError);
        
        // fallback إلى localStorage
        try {
          localStorage.setItem('qms_documents', JSON.stringify(updatedDocuments));
          console.log('تم حفظ المستند في localStorage كـ fallback');
        } catch (localStorageError) {
          console.error('فشل الحفظ في localStorage أيضاً:', localStorageError);
          
          if (localStorageError instanceof Error && localStorageError.name === 'QuotaExceededError') {
            // إذا امتلأ localStorage، امسح البيانات المؤقتة
            Object.keys(localStorage).forEach(key => {
              if (key.startsWith('temp_') || key.startsWith('cache_') || key.includes('backup')) {
                localStorage.removeItem(key);
              }
            });
            
            try {
              localStorage.setItem('qms_documents', JSON.stringify(updatedDocuments));
              console.log('تم حفظ المستند بعد تنظيف localStorage');
            } catch (finalError) {
              console.error('فشل نهائي في الحفظ:', finalError);
              throw new Error('لا يمكن حفظ المستند: مساحة التخزين ممتلئة. سيتم حفظه مؤقتاً في الذاكرة فقط.');
            }
          } else {
            throw localStorageError;
          }
        }
      }
      
      setDocuments(updatedDocuments);
      
    } catch (error) {
      console.error('خطأ في إضافة المستند:', error);
      throw error;
    }
  };

  const updateDocument = (updatedDoc: Partial<Document> & { id: string }) => {
    setDocuments(documents.map(doc =>
      doc.id === updatedDoc.id ? { ...doc, ...updatedDoc } : doc
    ));
  };

  const deleteDocument = (id: string) => {
    setDocuments(documents.filter(doc => doc.id !== id));
  };

  // User management
  const addUser = (user: Omit<User, 'id'>) => {
    const newUser = {
      ...user,
      id: `${Date.now()}`
    };
    setUsers([...users, newUser]);
  };

  const updateUser = (id: string, userData: Partial<Omit<User, 'id'>>) => {
    setUsers(users.map(user =>
      user.id === id ? { ...user, ...userData } : user
    ));
  };

  const deleteUser = (id: string) => {
    setUsers(users.filter(user => user.id !== id));
  };

  // User permissions
  const getUserPermissions = (userId: string) => {
    const user = users.find(u => u.id === userId);
    return user?.permissions || { view: [], download: [], manage: [] };
  };

  const updateUserPermissions = (userId: string, permissions: User['permissions']) => {
    setUsers(users.map(user =>
      user.id === userId ? { ...user, permissions } : user
    ));
  };

  const contextValue: AppContextType = {
    currentUser,
    isAuthenticated: !!currentUser,
    isAdmin: currentUser?.role === 'admin',
    sectors,
    categories,
    documents,
    login,
    logout,
    addSector,
    updateSector,
    deleteSector,
    addCategory,
    updateCategory,
    deleteCategory,
    addDocument,
    updateDocument,
    deleteDocument,
    getUserPermissions,
    updateUserPermissions,
    users,
    addUser,
    updateUser,
    deleteUser
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
};

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};
