
import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useApp } from '@/contexts/AppContext';
import Sidebar from './Sidebar';
import { Button } from './ui/button';
import { User, LogOut } from 'lucide-react';

interface LayoutProps {
  requireAuth?: boolean;
  requireAdmin?: boolean;
}

const Layout: React.FC<LayoutProps> = ({ requireAuth = false, requireAdmin = false }) => {
  const { isAuthenticated, isAdmin, currentUser, logout } = useApp();

  // Redirect unauthenticated users to login
  if (requireAuth && !isAuthenticated) {
    return <Navigate to="/login" />;
  }

  // Redirect non-admin users to documents page
  if (requireAdmin && !isAdmin) {
    return <Navigate to="/documents" />;
  }

  return (
    <div className="flex min-h-screen bg-background">
      {isAuthenticated && (
        <Sidebar />
      )}

      <div className="flex flex-col flex-1">
        {/* Header */}
        <header className="sticky top-0 z-10 bg-background border-b border-border h-16 flex items-center justify-between px-6">
          <h1 className="text-xl font-bold text-primary">
            نظام إدارة مستندات الجودة الداخلي
          </h1>

          {isAuthenticated ? (
            <div className="flex items-center gap-4">
              <div className="text-sm text-muted-foreground">
                <span className="text-primary font-medium">{currentUser?.username}</span>
                &nbsp;|&nbsp;
                <span>{currentUser?.role === 'admin' ? 'مدير النظام' : 'مستخدم'}</span>
              </div>
              <Button variant="ghost" size="icon" onClick={logout}>
                <LogOut className="h-5 w-5" />
              </Button>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <User className="h-5 w-5" />
              <span>غير مسجل دخول</span>
            </div>
          )}
        </header>

        {/* Main content */}
        <main className="flex-1 px-6 py-8">
          <Outlet />
        </main>

        {/* Footer */}
        <footer className="border-t border-border py-4 px-6 text-sm text-muted-foreground text-center">
          حقوق النشر © {new Date().getFullYear()} نظام إدارة مستندات الجودة الداخلي | تصميم وتطوير م. كريم وهيب | للتواصل: 01159296333
        </footer>
      </div>
    </div>
  );
};

export default Layout;
