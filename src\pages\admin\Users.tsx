import React, { useState } from 'react';
import { useApp } from '@/contexts/AppContext';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Plus, Trash, Edit } from 'lucide-react';
import { User } from '@/contexts/AppContext';

const Users: React.FC = () => {
  const { users, sectors, categories, addUser, updateUser, deleteUser } = useApp();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [newUsername, setNewUsername] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [newFullName, setNewFullName] = useState('');
  const [newPhoneNumber, setNewPhoneNumber] = useState('');
  const [newRole, setNewRole] = useState<'admin' | 'user'>('user');
  const [newSectors, setNewSectors] = useState<string[]>([]);
  const [permissions, setPermissions] = useState<{
    view: string[];
    download: string[];
    manage: string[];
  }>({
    view: [],
    download: [],
    manage: [],
  });
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const handleAddUser = () => {
    if (!newUsername || !newPassword) return;

    addUser({
      username: newUsername,
      password: newPassword,
      role: newRole,
      sectors: newSectors.length ? newSectors : ['all'],
      permissions,
      fullName: newFullName || undefined,
      phoneNumber: newPhoneNumber || undefined,
    });

    // Reset form
    setNewUsername('');
    setNewPassword('');
    setNewFullName('');
    setNewPhoneNumber('');
    setNewRole('user');
    setNewSectors([]);
    setPermissions({ view: [], download: [], manage: [] });
    setIsAddDialogOpen(false);
  };

  const handleEditUser = () => {
    if (!selectedUser) return;

    updateUser(selectedUser.id, {
      username: newUsername,
      password: newPassword,
      role: newRole,
      sectors: newSectors.length ? newSectors : ['all'],
      permissions,
      fullName: newFullName || undefined,
      phoneNumber: newPhoneNumber || undefined,
    });

    // Reset form
    setSelectedUser(null);
    setNewUsername('');
    setNewPassword('');
    setNewFullName('');
    setNewPhoneNumber('');
    setNewRole('user');
    setNewSectors([]);
    setPermissions({ view: [], download: [], manage: [] });
    setIsEditDialogOpen(false);
  };

  const handleEdit = (user: User) => {
    setSelectedUser(user);
    setNewUsername(user.username);
    setNewPassword(user.password || '');
    setNewFullName(user.fullName || '');
    setNewPhoneNumber(user.phoneNumber || '');
    setNewRole(user.role);
    setNewSectors(user.sectors);
    setPermissions(user.permissions);
    setIsEditDialogOpen(true);
  };

  const handleDeleteUser = (userId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
      deleteUser(userId);
    }
  };

  const toggleSectorSelection = (sectorId: string) => {
    if (newSectors.includes(sectorId)) {
      setNewSectors(newSectors.filter(id => id !== sectorId));
    } else {
      setNewSectors([...newSectors, sectorId]);
    }
  };

  const togglePermission = (type: keyof typeof permissions, categoryId: string) => {
    const currentPermissions = permissions[type];

    if (currentPermissions.includes(categoryId)) {
      setPermissions({
        ...permissions,
        [type]: currentPermissions.filter(id => id !== categoryId)
      });
    } else {
      setPermissions({
        ...permissions,
        [type]: [...currentPermissions, categoryId]
      });
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">إدارة المستخدمين</h1>

        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" /> إضافة مستخدم جديد
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>إضافة مستخدم جديد</DialogTitle>
              <DialogDescription>
                أدخل تفاصيل المستخدم الجديد وحدد الصلاحيات.
              </DialogDescription>
            </DialogHeader>

            {/* تقسيم النموذج إلى قسمين متجاورين */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
              {/* القسم الأول: البيانات الأساسية */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium border-b pb-2">البيانات الأساسية</h3>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="username" className="text-right">
                    اسم المستخدم
                  </Label>
                  <Input
                    id="username"
                    value={newUsername}
                    onChange={(e) => setNewUsername(e.target.value)}
                    className="col-span-3"
                    placeholder="أدخل اسم المستخدم"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="password" className="text-right">
                    كلمة المرور
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className="col-span-3"
                    placeholder="أدخل كلمة المرور"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="fullName" className="text-right">
                    الاسم الكامل
                  </Label>
                  <Input
                    id="fullName"
                    value={newFullName}
                    onChange={(e) => setNewFullName(e.target.value)}
                    className="col-span-3"
                    placeholder="أدخل الاسم الكامل (اختياري)"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="phoneNumber" className="text-right">
                    رقم الهاتف
                  </Label>
                  <Input
                    id="phoneNumber"
                    value={newPhoneNumber}
                    onChange={(e) => setNewPhoneNumber(e.target.value)}
                    className="col-span-3"
                    placeholder="أدخل رقم الهاتف (اختياري)"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="role" className="text-right">
                    الدور
                  </Label>
                  <Select value={newRole} onValueChange={(value: 'admin' | 'user') => setNewRole(value)}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="اختر الدور" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">مدير</SelectItem>
                      <SelectItem value="user">مستخدم</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="border rounded-md p-4">
                  <h4 className="text-sm font-medium mb-3">القطاعات</h4>
                  <div className="flex items-center space-x-2 mb-3">
                    <Checkbox
                      id="all-sectors"
                      checked={newSectors.includes('all')}
                      onCheckedChange={() => {
                        if (newSectors.includes('all')) {
                          setNewSectors([]);
                        } else {
                          setNewSectors(['all']);
                        }
                      }}
                    />
                    <label
                      htmlFor="all-sectors"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                    >
                      كل القطاعات
                    </label>
                  </div>

                  {!newSectors.includes('all') && (
                    <div className="grid grid-cols-2 gap-3 mt-3">
                      {sectors.map((sector) => (
                        <div key={sector.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`sector-${sector.id}`}
                            checked={newSectors.includes(sector.id)}
                            onCheckedChange={() => toggleSectorSelection(sector.id)}
                          />
                          <label
                            htmlFor={`sector-${sector.id}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                          >
                            {sector.name}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* القسم الثاني: الصلاحيات */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium border-b pb-2">الصلاحيات</h3>

                <div className="border rounded-md p-4">
                  <div className="mb-4">
                    <h4 className="text-sm font-medium mb-2">صلاحيات العرض</h4>
                    <div className="flex items-center space-x-2 mb-2">
                      <Checkbox
                        id="view-all"
                        checked={permissions.view.includes('all')}
                        onCheckedChange={() => {
                          if (permissions.view.includes('all')) {
                            setPermissions(prev => ({ ...prev, view: [] }));
                          } else {
                            setPermissions(prev => ({ ...prev, view: ['all'] }));
                          }
                        }}
                      />
                      <label
                        htmlFor="view-all"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                      >
                        كل التصنيفات
                      </label>
                    </div>

                    {!permissions.view.includes('all') && (
                      <div className="grid grid-cols-2 gap-2">
                        {categories.map((category) => (
                          <div key={category.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`view-${category.id}`}
                              checked={permissions.view.includes(category.id)}
                              onCheckedChange={() => togglePermission('view', category.id)}
                            />
                            <label
                              htmlFor={`view-${category.id}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                            >
                              {category.name}
                            </label>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="mb-4">
                    <h4 className="text-sm font-medium mb-2">صلاحيات التحميل</h4>
                    <div className="flex items-center space-x-2 mb-2">
                      <Checkbox
                        id="download-all"
                        checked={permissions.download.includes('all')}
                        onCheckedChange={() => {
                          if (permissions.download.includes('all')) {
                            setPermissions(prev => ({ ...prev, download: [] }));
                          } else {
                            setPermissions(prev => ({ ...prev, download: ['all'] }));
                          }
                        }}
                      />
                      <label
                        htmlFor="download-all"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                      >
                        كل التصنيفات
                      </label>
                    </div>

                    {!permissions.download.includes('all') && (
                      <div className="grid grid-cols-2 gap-2">
                        {categories.map((category) => (
                          <div key={category.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`download-${category.id}`}
                              checked={permissions.download.includes(category.id)}
                              onCheckedChange={() => togglePermission('download', category.id)}
                            />
                            <label
                              htmlFor={`download-${category.id}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                            >
                              {category.name}
                            </label>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {newRole === 'admin' && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">صلاحيات الإدارة</h4>
                      <div className="flex items-center space-x-2 mb-2">
                        <Checkbox
                          id="manage-all"
                          checked={permissions.manage.includes('all')}
                          disabled={newRole === 'admin'}
                          defaultChecked={newRole === 'admin'}
                        />
                        <label
                          htmlFor="manage-all"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                        >
                          كل التصنيفات (ممنوح للمدير)
                        </label>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                إلغاء
              </Button>
              <Button onClick={handleAddUser}>إضافة</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>تعديل المستخدم</DialogTitle>
              <DialogDescription>
                تعديل تفاصيل وصلاحيات المستخدم.
              </DialogDescription>
            </DialogHeader>

            {/* تقسيم النموذج إلى قسمين متجاورين */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
              {/* القسم الأول: البيانات الأساسية */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium border-b pb-2">البيانات الأساسية</h3>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-username" className="text-right">
                    اسم المستخدم
                  </Label>
                  <Input
                    id="edit-username"
                    value={newUsername}
                    onChange={(e) => setNewUsername(e.target.value)}
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-password" className="text-right">
                    كلمة المرور
                  </Label>
                  <Input
                    id="edit-password"
                    type="password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className="col-span-3"
                    placeholder="أدخل كلمة المرور الجديدة"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-fullName" className="text-right">
                    الاسم الكامل
                  </Label>
                  <Input
                    id="edit-fullName"
                    value={newFullName}
                    onChange={(e) => setNewFullName(e.target.value)}
                    className="col-span-3"
                    placeholder="أدخل الاسم الكامل (اختياري)"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-phoneNumber" className="text-right">
                    رقم الهاتف
                  </Label>
                  <Input
                    id="edit-phoneNumber"
                    value={newPhoneNumber}
                    onChange={(e) => setNewPhoneNumber(e.target.value)}
                    className="col-span-3"
                    placeholder="أدخل رقم الهاتف (اختياري)"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-role" className="text-right">
                    الدور
                  </Label>
                  <Select value={newRole} onValueChange={(value: 'admin' | 'user') => setNewRole(value)}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="اختر الدور" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">مدير</SelectItem>
                      <SelectItem value="user">مستخدم</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="border rounded-md p-4">
                  <h4 className="text-sm font-medium mb-3">القطاعات</h4>
                  <div className="flex items-center space-x-2 mb-3">
                    <Checkbox
                      id="edit-all-sectors"
                      checked={newSectors.includes('all')}
                      onCheckedChange={() => {
                        if (newSectors.includes('all')) {
                          setNewSectors([]);
                        } else {
                          setNewSectors(['all']);
                        }
                      }}
                    />
                    <label
                      htmlFor="edit-all-sectors"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                    >
                      كل القطاعات
                    </label>
                  </div>

                  {!newSectors.includes('all') && (
                    <div className="grid grid-cols-2 gap-3 mt-3">
                      {sectors.map((sector) => (
                        <div key={sector.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`edit-sector-${sector.id}`}
                            checked={newSectors.includes(sector.id)}
                            onCheckedChange={() => toggleSectorSelection(sector.id)}
                          />
                          <label
                            htmlFor={`edit-sector-${sector.id}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                          >
                            {sector.name}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* القسم الثاني: الصلاحيات */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium border-b pb-2">الصلاحيات</h3>

                <div className="border rounded-md p-4">
                  <div className="mb-4">
                    <h4 className="text-sm font-medium mb-2">صلاحيات العرض</h4>
                    <div className="flex items-center space-x-2 mb-2">
                      <Checkbox
                        id="edit-view-all"
                        checked={permissions.view.includes('all')}
                        onCheckedChange={() => {
                          if (permissions.view.includes('all')) {
                            setPermissions(prev => ({ ...prev, view: [] }));
                          } else {
                            setPermissions(prev => ({ ...prev, view: ['all'] }));
                          }
                        }}
                      />
                      <label
                        htmlFor="edit-view-all"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                      >
                        كل التصنيفات
                      </label>
                    </div>

                    {!permissions.view.includes('all') && (
                      <div className="grid grid-cols-2 gap-2">
                        {categories.map((category) => (
                          <div key={category.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`edit-view-${category.id}`}
                              checked={permissions.view.includes(category.id)}
                              onCheckedChange={() => togglePermission('view', category.id)}
                            />
                            <label
                              htmlFor={`edit-view-${category.id}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                            >
                              {category.name}
                            </label>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="mb-4">
                    <h4 className="text-sm font-medium mb-2">صلاحيات التحميل</h4>
                    <div className="flex items-center space-x-2 mb-2">
                      <Checkbox
                        id="edit-download-all"
                        checked={permissions.download.includes('all')}
                        onCheckedChange={() => {
                          if (permissions.download.includes('all')) {
                            setPermissions(prev => ({ ...prev, download: [] }));
                          } else {
                            setPermissions(prev => ({ ...prev, download: ['all'] }));
                          }
                        }}
                      />
                      <label
                        htmlFor="edit-download-all"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                      >
                        كل التصنيفات
                      </label>
                    </div>

                    {!permissions.download.includes('all') && (
                      <div className="grid grid-cols-2 gap-2">
                        {categories.map((category) => (
                          <div key={category.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`edit-download-${category.id}`}
                              checked={permissions.download.includes(category.id)}
                              onCheckedChange={() => togglePermission('download', category.id)}
                            />
                            <label
                              htmlFor={`edit-download-${category.id}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                            >
                              {category.name}
                            </label>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {newRole === 'admin' && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">صلاحيات الإدارة</h4>
                      <div className="flex items-center space-x-2 mb-2">
                        <Checkbox
                          id="edit-manage-all"
                          checked={permissions.manage.includes('all')}
                          disabled={newRole === 'admin'}
                          defaultChecked={newRole === 'admin'}
                        />
                        <label
                          htmlFor="edit-manage-all"
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                        >
                          كل التصنيفات (ممنوح للمدير)
                        </label>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                إلغاء
              </Button>
              <Button onClick={handleEditUser}>حفظ التغييرات</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>اسم المستخدم</TableHead>
              <TableHead>الاسم الكامل</TableHead>
              <TableHead>الدور</TableHead>
              <TableHead>القطاعات</TableHead>
              <TableHead>الصلاحيات</TableHead>
              <TableHead>الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map(user => (
              <TableRow key={user.id}>
                <TableCell className="font-medium">{user.username}</TableCell>
                <TableCell>{user.fullName || '-'}</TableCell>
                <TableCell>
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    user.role === 'admin' ? 'bg-primary/10 text-primary' : 'bg-muted text-muted-foreground'
                  }`}>
                    {user.role === 'admin' ? 'مدير' : 'مستخدم'}
                  </span>
                </TableCell>
                <TableCell>
                  {user.sectors.includes('all') ? (
                    <span>كل القطاعات</span>
                  ) : (
                    <span>
                      {user.sectors.length} قطاعات
                    </span>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex flex-col gap-1">
                    {user.permissions.view.includes('all') &&
                      <span className="text-xs text-green-600">عرض: الكل</span>}
                    {user.permissions.download.includes('all') &&
                      <span className="text-xs text-blue-600">تحميل: الكل</span>}
                    {user.permissions.manage.includes('all') &&
                      <span className="text-xs text-amber-600">إدارة: الكل</span>}

                    {!user.permissions.view.includes('all') &&
                      <span className="text-xs">عرض: {user.permissions.view.length} تصنيفات</span>}
                    {!user.permissions.download.includes('all') &&
                      <span className="text-xs">تحميل: {user.permissions.download.length} تصنيفات</span>}
                    {!user.permissions.manage.includes('all') &&
                      <span className="text-xs">إدارة: {user.permissions.manage.length} تصنيفات</span>}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="icon" onClick={() => handleEdit(user)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteUser(user.id)}
                      disabled={user.username === 'admin'} // Prevent deleting the main admin
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default Users;
