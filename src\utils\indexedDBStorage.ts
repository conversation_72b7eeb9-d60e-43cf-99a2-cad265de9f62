// IndexedDB Storage للملفات الكبيرة والأعداد الكبيرة
export class IndexedDBStorage {
  private dbName = 'QMSStorage';
  private version = 1;
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        console.error('خطأ في فتح IndexedDB:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('تم تهيئة IndexedDB بنجاح');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // إنشاء store للمستندات
        if (!db.objectStoreNames.contains('documents')) {
          const documentsStore = db.createObjectStore('documents', { keyPath: 'id' });
          documentsStore.createIndex('categoryId', 'categoryId', { unique: false });
          documentsStore.createIndex('sectorId', 'sectorId', { unique: false });
          documentsStore.createIndex('dateCreated', 'dateCreated', { unique: false });
        }

        // إنشاء store للملفات (منفصل لتحسين الأداء)
        if (!db.objectStoreNames.contains('files')) {
          const filesStore = db.createObjectStore('files', { keyPath: 'id' });
          filesStore.createIndex('documentId', 'documentId', { unique: false });
          filesStore.createIndex('name', 'name', { unique: false });
          filesStore.createIndex('type', 'type', { unique: false });
        }

        // إنشاء store للبيانات الأساسية (categories, sectors, users)
        if (!db.objectStoreNames.contains('appData')) {
          db.createObjectStore('appData', { keyPath: 'type' });
        }

        console.log('تم إنشاء قاعدة البيانات بنجاح');
      };
    });
  }

  // حفظ المستندات (بدون الملفات)
  async saveDocuments(documents: any[]): Promise<void> {
    if (!this.db) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['documents'], 'readwrite');
      const store = transaction.objectStore('documents');

      // مسح المستندات القديمة
      const clearRequest = store.clear();
      
      clearRequest.onsuccess = () => {
        // حفظ المستندات الجديدة (بدون الملفات لتوفير المساحة)
        const promises = documents.map(doc => {
          const docWithoutFiles = { ...doc, files: doc.files.map((f: any) => ({ id: f.id, name: f.name, type: f.type, size: f.size, dateUploaded: f.dateUploaded })) };
          return new Promise<void>((res, rej) => {
            const request = store.add(docWithoutFiles);
            request.onsuccess = () => res();
            request.onerror = () => rej(request.error);
          });
        });

        Promise.all(promises)
          .then(() => {
            console.log(`تم حفظ ${documents.length} مستند في IndexedDB`);
            resolve();
          })
          .catch(reject);
      };

      clearRequest.onerror = () => reject(clearRequest.error);
      transaction.onerror = () => reject(transaction.error);
    });
  }

  // حفظ الملفات (منفصل عن المستندات)
  async saveFiles(documentId: string, files: any[]): Promise<void> {
    if (!this.db) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['files'], 'readwrite');
      const store = transaction.objectStore('files');

      // حذف الملفات القديمة لهذا المستند
      const index = store.index('documentId');
      const range = IDBKeyRange.only(documentId);
      const deleteRequest = index.openCursor(range);
      
      const filesToDelete: IDBValidKey[] = [];
      
      deleteRequest.onsuccess = () => {
        const cursor = deleteRequest.result;
        if (cursor) {
          filesToDelete.push(cursor.primaryKey);
          cursor.continue();
        } else {
          // حذف الملفات القديمة
          const deletePromises = filesToDelete.map(key => {
            return new Promise<void>((res, rej) => {
              const delReq = store.delete(key);
              delReq.onsuccess = () => res();
              delReq.onerror = () => rej(delReq.error);
            });
          });

          Promise.all(deletePromises).then(() => {
            // إضافة الملفات الجديدة
            const addPromises = files.map(file => {
              const fileWithDocId = { ...file, documentId };
              return new Promise<void>((res, rej) => {
                const request = store.add(fileWithDocId);
                request.onsuccess = () => res();
                request.onerror = () => rej(request.error);
              });
            });

            Promise.all(addPromises)
              .then(() => {
                console.log(`تم حفظ ${files.length} ملف للمستند ${documentId}`);
                resolve();
              })
              .catch(reject);
          }).catch(reject);
        }
      };

      deleteRequest.onerror = () => reject(deleteRequest.error);
      transaction.onerror = () => reject(transaction.error);
    });
  }

  // تحميل المستندات
  async loadDocuments(): Promise<any[]> {
    if (!this.db) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['documents', 'files'], 'readonly');
      const documentsStore = transaction.objectStore('documents');
      const filesStore = transaction.objectStore('files');

      const request = documentsStore.getAll();
      
      request.onsuccess = async () => {
        const documents = request.result;
        
        // تحميل الملفات لكل مستند
        const documentsWithFiles = await Promise.all(
          documents.map(async (doc) => {
            const files = await this.loadFilesForDocument(doc.id);
            return { ...doc, files };
          })
        );

        console.log(`تم تحميل ${documentsWithFiles.length} مستند من IndexedDB`);
        resolve(documentsWithFiles);
      };

      request.onerror = () => reject(request.error);
    });
  }

  // تحميل ملفات مستند معين
  private async loadFilesForDocument(documentId: string): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['files'], 'readonly');
      const store = transaction.objectStore('files');
      const index = store.index('documentId');
      const range = IDBKeyRange.only(documentId);
      const request = index.getAll(range);

      request.onsuccess = () => {
        resolve(request.result || []);
      };

      request.onerror = () => reject(request.error);
    });
  }

  // حفظ البيانات الأساسية (categories, sectors, users)
  async saveAppData(type: string, data: any[]): Promise<void> {
    if (!this.db) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['appData'], 'readwrite');
      const store = transaction.objectStore('appData');

      const request = store.put({ type, data });
      
      request.onsuccess = () => {
        console.log(`تم حفظ ${type} في IndexedDB`);
        resolve();
      };

      request.onerror = () => reject(request.error);
    });
  }

  // تحميل البيانات الأساسية
  async loadAppData(type: string): Promise<any[]> {
    if (!this.db) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['appData'], 'readonly');
      const store = transaction.objectStore('appData');

      const request = store.get(type);
      
      request.onsuccess = () => {
        const result = request.result;
        resolve(result ? result.data : []);
      };

      request.onerror = () => reject(request.error);
    });
  }

  // حذف مستند وملفاته
  async deleteDocument(documentId: string): Promise<void> {
    if (!this.db) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['documents', 'files'], 'readwrite');
      const documentsStore = transaction.objectStore('documents');
      const filesStore = transaction.objectStore('files');

      // حذف المستند
      const deleteDocRequest = documentsStore.delete(documentId);
      
      // حذف ملفات المستند
      const index = filesStore.index('documentId');
      const range = IDBKeyRange.only(documentId);
      const deleteFilesRequest = index.openCursor(range);
      
      const filesToDelete: IDBValidKey[] = [];
      
      deleteFilesRequest.onsuccess = () => {
        const cursor = deleteFilesRequest.result;
        if (cursor) {
          filesToDelete.push(cursor.primaryKey);
          cursor.continue();
        } else {
          const deletePromises = filesToDelete.map(key => {
            return new Promise<void>((res, rej) => {
              const delReq = filesStore.delete(key);
              delReq.onsuccess = () => res();
              delReq.onerror = () => rej(delReq.error);
            });
          });

          Promise.all(deletePromises)
            .then(() => {
              console.log(`تم حذف المستند ${documentId} وجميع ملفاته`);
              resolve();
            })
            .catch(reject);
        }
      };

      transaction.onerror = () => reject(transaction.error);
    });
  }

  // فحص مساحة التخزين المستخدمة
  async getStorageUsage(): Promise<{ used: number; available: number }> {
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate();
        return {
          used: estimate.usage || 0,
          available: estimate.quota || 0
        };
      }
    } catch (error) {
      console.error('لا يمكن قياس مساحة التخزين:', error);
    }
    
    return { used: 0, available: Infinity };
  }

  // تنظيف قاعدة البيانات
  async clearAll(): Promise<void> {
    if (!this.db) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['documents', 'files', 'appData'], 'readwrite');
      
      const promises = [
        new Promise<void>((res, rej) => {
          const req = transaction.objectStore('documents').clear();
          req.onsuccess = () => res();
          req.onerror = () => rej(req.error);
        }),
        new Promise<void>((res, rej) => {
          const req = transaction.objectStore('files').clear();
          req.onsuccess = () => res();
          req.onerror = () => rej(req.error);
        }),
        new Promise<void>((res, rej) => {
          const req = transaction.objectStore('appData').clear();
          req.onsuccess = () => res();
          req.onerror = () => rej(req.error);
        })
      ];

      Promise.all(promises)
        .then(() => {
          console.log('تم تنظيف قاعدة البيانات بالكامل');
          resolve();
        })
        .catch(reject);

      transaction.onerror = () => reject(transaction.error);
    });
  }
}

// إنشاء instance مشترك
export const indexedDBStorage = new IndexedDBStorage();