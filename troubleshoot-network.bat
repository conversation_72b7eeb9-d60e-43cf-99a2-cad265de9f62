@echo off
chcp 65001 >nul
echo ========================================
echo   تشخيص مشاكل الشبكة - QMS Flow
echo ========================================
echo.

echo 🔍 فحص إعدادات الشبكة...
echo.

echo 1️⃣ عناوين IP المتاحة:
echo ========================
ipconfig | findstr /i "IPv4"
echo.

echo 2️⃣ فحص المنافذ المستخدمة:
echo =========================
echo جاري فحص المنفذ 3001 (قاعدة البيانات)...
netstat -an | findstr :3001
if %errorlevel%==0 (
    echo ✅ المنفذ 3001 مستخدم
) else (
    echo ❌ المنفذ 3001 غير مستخدم - قاعدة البيانات لا تعمل
)

echo.
echo جاري فحص المنفذ 5173 (التطبيق)...
netstat -an | findstr :5173
if %errorlevel%==0 (
    echo ✅ المنفذ 5173 مستخدم
) else (
    echo ❌ المنفذ 5173 غير مستخدم - التطبيق لا يعمل
)

echo.
echo 3️⃣ فحص إعدادات Firewall:
echo =========================
echo تحقق من إعدادات Windows Firewall:
echo - اذهب إلى Control Panel > System and Security > Windows Defender Firewall
echo - انقر على "Allow an app or feature through Windows Defender Firewall"
echo - تأكد من السماح لـ Node.js أو npm

echo.
echo 4️⃣ اختبار الاتصال المحلي:
echo =========================
echo جاري اختبار الاتصال بـ localhost...

ping -n 1 localhost >nul 2>&1
if %errorlevel%==0 (
    echo ✅ localhost يعمل
) else (
    echo ❌ مشكلة في localhost
)

echo.
echo 5️⃣ الحلول المقترحة:
echo ===================
echo إذا كانت المشكلة في عدم ظهور البيانات على الأجهزة الأخرى:
echo.
echo أ) تأكد من تشغيل النظام الكامل:
echo    - استخدم start-full-network.bat (وليس simple-network.bat)
echo    - تأكد من ظهور نافذتين (قاعدة البيانات + التطبيق)
echo.
echo ب) تحقق من إعدادات الشبكة:
echo    - تأكد من اتصال جميع الأجهزة بنفس الشبكة
echo    - جرب تعطيل Windows Firewall مؤقتاً
echo.
echo ج) اختبر الاتصال:
echo    - من جهاز آخر، افتح المتصفح
echo    - اذهب إلى http://[IP-ADDRESS]:3001/users
echo    - يجب أن تظهر قائمة المستخدمين
echo.
echo د) إعادة تشغيل النظام:
echo    - أغلق جميع النوافذ (Ctrl+C)
echo    - شغل start-full-network.bat مرة أخرى
echo    - انتظر حتى يظهر "ready" في كلا النافذتين
echo.
echo 6️⃣ للدعم الفني:
echo ================
echo إذا استمرت المشكلة، تحقق من:
echo - إصدار Node.js: node --version
echo - حالة npm: npm --version
echo - ملف db.json موجود ويحتوي على بيانات
echo.
pause
