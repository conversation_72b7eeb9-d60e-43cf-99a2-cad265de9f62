import React, { useState } from 'react';
import PdfViewer from './PdfViewer';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Upload, FileText } from 'lucide-react';

const PdfViewerTest: React.FC = () => {
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [fileName, setFileName] = useState<string>('');

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'application/pdf') {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setPdfUrl(result);
        setFileName(file.name);
      };
      reader.readAsDataURL(file);
    } else {
      alert('يرجى اختيار ملف PDF صالح');
    }
  };

  const testWithSamplePdf = () => {
    // استخدام PDF تجريبي بسيط من الإنترنت (يعمل بشكل أفضل)
    const samplePdfUrl = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';
    setPdfUrl(samplePdfUrl);
    setFileName('ملف PDF تجريبي من الإنترنت');
    setIsViewerOpen(true);
  };

  const testWithLocalPdf = () => {
    // إنشاء PDF بسيط محلياً للاختبار
    const createLocalPdf = () => {
      const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj
4 0 obj
<<
/Length 85
>>
stream
BT
/F1 24 Tf
100 700 Td
(PDF Test Document) Tj
0 -50 Td
/F1 16 Tf
(This is a test PDF created locally) Tj
ET
endstream
endobj
5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj
xref
0 6
0000000000 65535 f
0000000009 00000 n
0000000058 00000 n
0000000115 00000 n
0000000274 00000 n
0000000410 00000 n
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
479
%%EOF`;

      const blob = new Blob([pdfContent], { type: 'application/pdf' });
      return URL.createObjectURL(blob);
    };

    const localPdfUrl = createLocalPdf();
    setPdfUrl(localPdfUrl);
    setFileName('ملف PDF محلي تجريبي');
    setIsViewerOpen(true);
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-2xl font-bold mb-2">اختبار عارض PDF</h1>
        <p className="text-gray-600">اختبر عارض PDF المحسن</p>
      </div>

      <div className="space-y-4">
        {/* رفع ملف PDF */}
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium mb-2">ارفع ملف PDF للاختبار</h3>
          <input
            type="file"
            accept=".pdf"
            onChange={handleFileUpload}
            className="hidden"
            id="pdf-upload"
          />
          <label
            htmlFor="pdf-upload"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer"
          >
            <Upload className="h-4 w-4 mr-2" />
            اختر ملف PDF
          </label>
        </div>

        {/* أو استخدام ملف تجريبي */}
        <div className="text-center">
          <p className="text-gray-500 mb-2">أو</p>
          <div className="flex gap-2 justify-center">
            <Button onClick={testWithSamplePdf} variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              PDF من الإنترنت
            </Button>
            <Button onClick={testWithLocalPdf} variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              PDF محلي
            </Button>
          </div>
        </div>

        {/* عرض معلومات الملف المحدد */}
        {pdfUrl && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-green-800">ملف جاهز للعرض</h4>
                <p className="text-sm text-green-600">{fileName}</p>
              </div>
              <Dialog open={isViewerOpen} onOpenChange={setIsViewerOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <FileText className="h-4 w-4 mr-2" />
                    عرض PDF
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl h-[90vh]">
                  <DialogHeader>
                    <DialogTitle>{fileName}</DialogTitle>
                  </DialogHeader>
                  <div className="h-full">
                    <PdfViewer fileUrl={pdfUrl} fileName={fileName} />
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        )}
      </div>

      {/* معلومات الاختبار */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-medium text-blue-800 mb-2">معلومات الاختبار</h3>
        <ul className="text-sm text-blue-600 space-y-1">
          <li>• يستخدم iframe لعرض PDF (بسيط وموثوق)</li>
          <li>• يحول data URLs إلى blob URLs تلقائياً</li>
          <li>• يدعم فتح PDF في نافذة جديدة مع معالجة خاصة</li>
          <li>• خيارات بديلة: تحميل أو فتح في نافذة جديدة</li>
          <li>• لا يحتاج إنترنت للعمل (عدا الملف التجريبي من الإنترنت)</li>
          <li>• معالجة شاملة للأخطاء</li>
        </ul>
      </div>

      {/* حالة الاختبار */}
      <div className="mt-4 text-center">
        <div className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
          ✅ عارض PDF جاهز للاستخدام
        </div>
      </div>
    </div>
  );
};

export default PdfViewerTest;
