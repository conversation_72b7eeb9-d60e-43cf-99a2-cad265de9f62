import React, { useState } from 'react';
import PdfViewer from './PdfViewer';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Upload, FileText } from 'lucide-react';

const PdfViewerTest: React.FC = () => {
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [fileName, setFileName] = useState<string>('');

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'application/pdf') {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setPdfUrl(result);
        setFileName(file.name);
      };
      reader.readAsDataURL(file);
    } else {
      alert('يرجى اختيار ملف PDF صالح');
    }
  };

  const testWithSamplePdf = () => {
    // استخدام PDF تجريبي من الإنترنت للاختبار
    const samplePdfUrl = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf';
    setPdfUrl(samplePdfUrl);
    setFileName('ملف PDF تجريبي');
    setIsViewerOpen(true);
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-2xl font-bold mb-2">اختبار عارض PDF</h1>
        <p className="text-gray-600">اختبر عارض PDF المحسن</p>
      </div>

      <div className="space-y-4">
        {/* رفع ملف PDF */}
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium mb-2">ارفع ملف PDF للاختبار</h3>
          <input
            type="file"
            accept=".pdf"
            onChange={handleFileUpload}
            className="hidden"
            id="pdf-upload"
          />
          <label
            htmlFor="pdf-upload"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer"
          >
            <Upload className="h-4 w-4 mr-2" />
            اختر ملف PDF
          </label>
        </div>

        {/* أو استخدام ملف تجريبي */}
        <div className="text-center">
          <p className="text-gray-500 mb-2">أو</p>
          <Button onClick={testWithSamplePdf} variant="outline">
            <FileText className="h-4 w-4 mr-2" />
            استخدم ملف PDF تجريبي
          </Button>
        </div>

        {/* عرض معلومات الملف المحدد */}
        {pdfUrl && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-green-800">ملف جاهز للعرض</h4>
                <p className="text-sm text-green-600">{fileName}</p>
              </div>
              <Dialog open={isViewerOpen} onOpenChange={setIsViewerOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <FileText className="h-4 w-4 mr-2" />
                    عرض PDF
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl h-[90vh]">
                  <DialogHeader>
                    <DialogTitle>{fileName}</DialogTitle>
                  </DialogHeader>
                  <div className="h-full">
                    <PdfViewer fileUrl={pdfUrl} />
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        )}
      </div>

      {/* معلومات الاختبار */}
      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="font-medium text-blue-800 mb-2">معلومات الاختبار</h3>
        <ul className="text-sm text-blue-600 space-y-1">
          <li>• يدعم العارض ملفات PDF من جميع الأحجام</li>
          <li>• يتم تحويل data URLs إلى blob URLs تلقائياً لتحسين الأداء</li>
          <li>• يتضمن معالجة شاملة للأخطاء</li>
          <li>• يدعم التنقل بين الصفحات</li>
          <li>• يعرض مؤشرات التحميل</li>
        </ul>
      </div>

      {/* حالة الاختبار */}
      <div className="mt-4 text-center">
        <div className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800">
          ✅ عارض PDF جاهز للاستخدام
        </div>
      </div>
    </div>
  );
};

export default PdfViewerTest;
