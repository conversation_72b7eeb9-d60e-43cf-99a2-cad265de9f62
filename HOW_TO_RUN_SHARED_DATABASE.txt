========================================
   تشغيل QMS Flow مع قاعدة البيانات المشتركة
========================================

🎯 الهدف: تشغيل النظام بحيث تكون البيانات مشتركة بين جميع الأجهزة على الشبكة

الطريقة الموصى بها: التشغيل الكامل
===================================
1. انقر نقراً مزدوجاً على: start-full-network.bat
2. ستظهر نافذتان:
   - نافذة قاعدة البيانات (Database Server)
   - نافذة التطبيق (Application Server)
3. انتظر حتى يظهر "ready" في كلا النافذتين
4. اترك النافذتين مفتوحتين (لا تغلقهما)

البدائل للتشغيل
===============
أ) التشغيل الكامل (موصى به):
   - ملف: start-full-network.bat
   - يشغل قاعدة البيانات + التطبيق
   - البيانات مشتركة بين جميع الأجهزة

ب) التشغيل البسيط:
   - ملف: simple-network.bat
   - يشغل التطبيق فقط
   - البيانات محلية (غير مشتركة)

معرفة عنوان IP
==============
1. انقر نقراً مزدوجاً على: get-ip.bat
2. ابحث عن السطر الذي يحتوي على "192.168" أو "10.0"
3. هذا هو عنوان IP المحلي

الوصول للتطبيق
==============
من نفس الجهاز:
http://localhost:5173

من أجهزة أخرى في الشبكة:
http://[عنوان-IP]:5173

مثال:
http://*************:5173

قاعدة البيانات
=============
العنوان: http://[عنوان-IP]:3001
الملف: db.json (يحتوي على جميع البيانات)

مميزات النظام الجديد
===================
✅ المستخدمون المضافون يظهرون على جميع الأجهزة
✅ المستندات والملفات متاحة للجميع
✅ تسجيل الدخول يعمل من أي جهاز
✅ البيانات محفوظة حتى بعد إغلاق التطبيق
✅ يعمل بدون إنترنت (شبكة محلية فقط)

كيف يعمل النظام
===============
1. قاعدة البيانات تعمل على المنفذ 3001
2. التطبيق يعمل على المنفذ 5173
3. جميع الأجهزة تتصل بنفس قاعدة البيانات
4. البيانات محفوظة في ملف db.json

استكشاف الأخطاء
===============
❌ المشكلة: لا تظهر البيانات المشتركة على الأجهزة الأخرى
✅ الحل:
   1. تأكد من تشغيل قاعدة البيانات على المنفذ 3001
   2. من جهاز آخر، اختبر: http://[IP-ADDRESS]:3001/users
   3. يجب أن تظهر قائمة المستخدمين

❌ المشكلة: خطأ "EADDRINUSE"
✅ الحل: أغلق التطبيقات الأخرى التي تستخدم نفس المنفذ

❌ المشكلة: الأجهزة الأخرى لا تستطيع الوصول
✅ الحل:
   1. تحقق من إعدادات Windows Firewall
   2. جرب تعطيل Firewall مؤقتاً للاختبار
   3. تأكد من السماح للمنافذ 3001 و 5173

❌ المشكلة: البيانات لا تحفظ
✅ الحل: تأكد من وجود ملف db.json وأن قاعدة البيانات تعمل

❌ المشكلة: التطبيق يعمل محلياً فقط
✅ الحل: تم إصلاح هذه المشكلة - التطبيق الآن يكتشف عنوان IP تلقائياً

ملاحظات مهمة
============
⚠️ يجب أن يبقى الجهاز المضيف مشغلاً طوال فترة الاستخدام
⚠️ لا تغلق نوافذ قاعدة البيانات والتطبيق
⚠️ تأكد من أن جميع الأجهزة متصلة بنفس الشبكة
⚠️ احتفظ بنسخة احتياطية من ملف db.json

اختبار الشبكة
============
للتأكد من عمل النظام على الشبكة:

1. شغل: test-network-connection.bat
   - يختبر قاعدة البيانات والتطبيق
   - يعطيك عنوان IP الصحيح

2. من جهاز آخر على الشبكة:
   - افتح المتصفح
   - اذهب إلى: http://[IP-ADDRESS]:3001/users
   - يجب أن تظهر قائمة المستخدمين

3. اختبار التطبيق:
   - اذهب إلى: http://[IP-ADDRESS]:5173
   - سجل دخول بحساب موجود
   - أنشئ مستخدم جديد
   - تحقق من ظهوره على الأجهزة الأخرى

الدعم الفني
===========
إذا واجهت مشاكل:
1. شغل: troubleshoot-network.bat للتشخيص
2. تأكد من تثبيت Node.js
3. جرب إعادة تشغيل التطبيق
4. تحقق من إعدادات الشبكة والـ Firewall
5. راجع ملف db.json للتأكد من سلامة البيانات

ملفات المساعدة:
- test-network-connection.bat: اختبار الاتصال
- troubleshoot-network.bat: تشخيص المشاكل
