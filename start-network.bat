@echo off
chcp 65001 >nul
title QMS Flow - تشغيل على الشبكة المحلية

echo.
echo ================================================
echo           QMS Flow - نظام إدارة الجودة
echo ================================================
echo.
echo جاري تشغيل التطبيق على الشبكة المحلية...
echo.

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت على النظام
    echo يرجى تثبيت Node.js من: https://nodejs.org
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: npm غير متاح
    echo يرجى إعادة تثبيت Node.js
    echo.
    pause
    exit /b 1
)

REM التحقق من وجود package.json
if not exist "package.json" (
    echo ❌ خطأ: لم يتم العثور على package.json
    echo تأكد من تشغيل الاسكريبت في مجلد المشروع الصحيح
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js متاح - الإصدار:
node --version
echo.

echo ✅ npm متاح - الإصدار:
npm --version
echo.

REM الحصول على عنوان IP المحلي
echo جاري الحصول على عنوان IP المحلي...
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /i "IPv4"') do (
    for /f "tokens=1" %%j in ("%%i") do (
        set LOCAL_IP=%%j
        goto :found_ip
    )
)

:found_ip
if defined LOCAL_IP (
    echo ✅ عنوان IP المحلي: %LOCAL_IP%
) else (
    echo ⚠️  لم يتم العثور على عنوان IP المحلي
    set LOCAL_IP=localhost
)

echo.
echo ================================================
echo                معلومات الاتصال
echo ================================================
echo.
echo 🌐 الوصول المحلي:     http://localhost:5173
echo 🌐 الوصول من الشبكة:  http://%LOCAL_IP%:5173
echo.
echo ملاحظة: تأكد من أن جدار الحماية يسمح بالاتصالات
echo         على المنفذ 5173
echo.
echo ================================================
echo.

REM تثبيت التبعيات إذا لم تكن موجودة
if not exist "node_modules" (
    echo 📦 جاري تثبيت التبعيات...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت التبعيات
        echo.
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
    echo.
)

echo 🚀 جاري تشغيل التطبيق...
echo.
echo للإيقاف: اضغط Ctrl+C
echo.

REM تشغيل التطبيق مع إعدادات الشبكة
npm run dev -- --host 0.0.0.0 --port 5173

echo.
echo تم إيقاف التطبيق
pause
