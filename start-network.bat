@echo off
title QMS Flow - Network Server

echo.
echo ================================================
echo           QMS Flow - Quality Management System
echo ================================================
echo.
echo Starting application on local network...
echo.

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed
    echo Please install Node.js from: https://nodejs.org
    echo.
    pause
    exit /b 1
)

REM Check npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not available
    echo Please reinstall Node.js
    echo.
    pause
    exit /b 1
)

REM Check package.json
if not exist "package.json" (
    echo ERROR: package.json not found
    echo Make sure to run this script in the correct project folder
    echo.
    pause
    exit /b 1
)

echo Node.js version:
node --version
echo.

echo npm version:
npm --version
echo.

REM Get local IP address
echo Getting local IP address...
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /i "IPv4"') do (
    for /f "tokens=1" %%j in ("%%i") do (
        set LOCAL_IP=%%j
        goto :found_ip
    )
)

:found_ip
if defined LOCAL_IP (
    echo Local IP address: %LOCAL_IP%
) else (
    echo Could not find local IP address
    set LOCAL_IP=localhost
)

echo.
echo ================================================
echo                Connection Info
echo ================================================
echo.
echo Local access:     http://localhost:5173
echo Network access:   http://%LOCAL_IP%:5173
echo.
echo Note: Make sure firewall allows connections on port 5173
echo.
echo ================================================
echo.

REM Install dependencies if not exist
if not exist "node_modules" (
    echo Installing dependencies...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo Failed to install dependencies
        echo.
        pause
        exit /b 1
    )
    echo Dependencies installed successfully
    echo.
)

echo Starting application...
echo.
echo To stop: Press Ctrl+C
echo.

REM Start application with network settings
npm run dev -- --host 0.0.0.0 --port 5173

echo.
echo Application stopped
pause
