
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useApp } from '@/contexts/AppContext';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Download, Upload, Trash } from 'lucide-react';

const Settings: React.FC = () => {
  const { categories, sectors, documents, users } = useApp();

  const handleExportData = () => {
    const data = {
      categories,
      sectors,
      documents,
      users
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `qms-data-export-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('تم تصدير البيانات بنجاح');
  };

  const handleImportData = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        const text = await file.text();
        const data = JSON.parse(text);
        
        // In a real app, we would validate the data structure here
        
        // Then import the data by updating localStorage
        if (data.categories) localStorage.setItem('qms_categories', JSON.stringify(data.categories));
        if (data.sectors) localStorage.setItem('qms_sectors', JSON.stringify(data.sectors));
        if (data.documents) localStorage.setItem('qms_documents', JSON.stringify(data.documents));
        if (data.users) localStorage.setItem('qms_users', JSON.stringify(data.users));
        
        toast.success('تم استيراد البيانات بنجاح، قم بتحديث الصفحة');
        
        // In a real app with proper state management, we would update the state here
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } catch (err) {
        console.error(err);
        toast.error('فشل استيراد البيانات');
      }
    };
    input.click();
  };

  const handleClearData = () => {
    if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
      localStorage.removeItem('qms_categories');
      localStorage.removeItem('qms_sectors');
      localStorage.removeItem('qms_documents');
      // Don't remove users to maintain login functionality
      
      toast.success('تم مسح البيانات بنجاح، قم بتحديث الصفحة');
      
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    }
  };

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">الإعدادات</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>إدارة البيانات</CardTitle>
            <CardDescription>
              استيراد وتصدير بيانات النظام
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4">
              <Button 
                variant="outline" 
                className="justify-start"
                onClick={handleExportData}
              >
                <Download className="h-4 w-4 mr-2" />
                تصدير جميع البيانات
              </Button>
              
              <Button 
                variant="outline" 
                className="justify-start"
                onClick={handleImportData}
              >
                <Upload className="h-4 w-4 mr-2" />
                استيراد البيانات
              </Button>
              
              <Separator className="my-2" />
              
              <Button 
                variant="destructive" 
                className="justify-start"
                onClick={handleClearData}
              >
                <Trash className="h-4 w-4 mr-2" />
                مسح البيانات
              </Button>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>حول النظام</CardTitle>
            <CardDescription>
              معلومات حول نظام إدارة مستندات الجودة
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium">الإصدار</h3>
                <p className="text-sm text-muted-foreground">1.0.0</p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium">حول</h3>
                <p className="text-sm text-muted-foreground">
                  نظام إدارة مستندات الجودة الداخلي هو تطبيق ويب يسمح للشركات بإدارة ومشاركة مستندات ضبط الجودة داخليًا مع الموظفين حسب الصلاحيات.
                </p>
              </div>
              
              <div>
                <h3 className="text-sm font-medium">التخزين</h3>
                <p className="text-sm text-muted-foreground">
                  يستخدم هذا النظام تخزين المتصفح المحلي (localStorage) لحفظ البيانات. في بيئة الإنتاج الحقيقية، يُوصى باستخدام قاعدة بيانات سحابية أو محلية.
                </p>
              </div>
              
              <div className="pt-4">
                <p className="text-xs text-muted-foreground text-center">
                  حقوق النشر © {new Date().getFullYear()} نظام إدارة مستندات الجودة الداخلي
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Settings;
