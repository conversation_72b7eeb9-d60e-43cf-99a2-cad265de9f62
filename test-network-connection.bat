@echo off
chcp 65001 >nul
echo ========================================
echo   اختبار اتصال الشبكة - QMS Flow
echo ========================================
echo.

echo 🔍 جاري فحص عنوان IP...
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /i "IPv4"') do (
    for /f "tokens=1" %%j in ("%%i") do (
        set "ip=%%j"
        if not "!ip!"=="" (
            echo ✅ عنوان IP: !ip!
            goto :found_ip
        )
    )
)

:found_ip
if "%ip%"=="" (
    echo ❌ لم يتم العثور على عنوان IP
    pause
    exit /b 1
)

echo.
echo 🔍 جاري اختبار قاعدة البيانات...
echo المنفذ: 3001
echo العنوان: http://%ip%:3001

curl -s http://%ip%:3001/users >nul 2>&1
if %errorlevel%==0 (
    echo ✅ قاعدة البيانات تعمل بشكل صحيح
) else (
    echo ❌ قاعدة البيانات لا تعمل
    echo تأكد من تشغيل: start-full-network.bat
)

echo.
echo 🔍 جاري اختبار التطبيق...
echo المنفذ: 5173
echo العنوان: http://%ip%:5173

curl -s http://%ip%:5173 >nul 2>&1
if %errorlevel%==0 (
    echo ✅ التطبيق يعمل بشكل صحيح
) else (
    echo ❌ التطبيق لا يعمل
    echo تأكد من تشغيل: start-full-network.bat
)

echo.
echo 📋 ملخص الاختبار:
echo ==================
echo عنوان IP: %ip%
echo رابط التطبيق: http://%ip%:5173
echo رابط قاعدة البيانات: http://%ip%:3001
echo.
echo 💡 لاختبار من جهاز آخر:
echo 1. تأكد من اتصال الجهاز بنفس الشبكة
echo 2. افتح المتصفح واذهب إلى: http://%ip%:5173
echo 3. جرب إنشاء مستخدم جديد
echo 4. تحقق من ظهوره على الأجهزة الأخرى
echo.
pause
