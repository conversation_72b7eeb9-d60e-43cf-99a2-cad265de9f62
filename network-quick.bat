@echo off
title QMS Flow - Quick Network Start

echo.
echo Starting QMS Flow on local network...
echo.

REM Get local IP quickly
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /i "IPv4" ^| findstr /v "127.0.0.1"') do (
    for /f "tokens=1" %%j in ("%%i") do (
        set LOCAL_IP=%%j
        goto :found
    )
)

:found
if defined LOCAL_IP (
    echo Network access: http://%LOCAL_IP%:5173
) else (
    echo Local access: http://localhost:5173
)

echo Local access: http://localhost:5173
echo.
echo To stop: Press Ctrl+C
echo.

npm run dev:network
pause
