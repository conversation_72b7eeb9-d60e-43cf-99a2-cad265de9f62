@echo off
chcp 65001 >nul
title QMS Flow - تشغيل سريع على الشبكة

echo.
echo 🚀 تشغيل QMS Flow على الشبكة المحلية...
echo.

REM الحصول على IP المحلي بسرعة
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr /i "IPv4" ^| findstr /v "127.0.0.1"') do (
    for /f "tokens=1" %%j in ("%%i") do (
        set LOCAL_IP=%%j
        goto :found
    )
)

:found
if defined LOCAL_IP (
    echo 🌐 الوصول من الشبكة: http://%LOCAL_IP%:5173
) else (
    echo 🌐 الوصول المحلي: http://localhost:5173
)

echo 🌐 الوصول المحلي: http://localhost:5173
echo.
echo للإيقاف: اضغط Ctrl+C
echo.

npm run dev:network
