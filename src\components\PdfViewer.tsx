import React, { useRef, useEffect, useState } from 'react';
import * as pdfjsLib from 'pdfjs-dist';
import 'pdfjs-dist/web/pdf_viewer.css';

pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;

interface PdfViewerProps {
  fileUrl: string;
}

const PdfViewer: React.FC<PdfViewerProps> = ({ fileUrl }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [pdf, setPdf] = useState<pdfjsLib.PDFDocumentProxy | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [numPages, setNumPages] = useState(0);

  useEffect(() => {
    const loadingTask = pdfjsLib.getDocument(fileUrl);
    loadingTask.promise.then(pdfDoc => {
      setPdf(pdfDoc);
      setNumPages(pdfDoc.numPages);
    });
  }, [fileUrl]);

  useEffect(() => {
    if (pdf) {
      pdf.getPage(pageNumber).then(page => {
        const canvas = canvasRef.current;
        if (canvas) {
          const context = canvas.getContext('2d');
          if (context) {
            const viewport = page.getViewport({ scale: 1.5 });
            canvas.height = viewport.height;
            canvas.width = viewport.width;
            const renderContext = {
              canvasContext: context,
              viewport: viewport
            };
            page.render(renderContext);
          }
        }
      });
    }
  }, [pdf, pageNumber]);

  const onPrevPage = () => {
    if (pageNumber > 1) {
      setPageNumber(pageNumber - 1);
    }
  };

  const onNextPage = () => {
    if (pageNumber < numPages) {
      setPageNumber(pageNumber + 1);
    }
  };

  return (
    <div>
      <div>
        <button onClick={onPrevPage} disabled={pageNumber <= 1}>Previous</button>
        <span>Page {pageNumber} of {numPages}</span>
        <button onClick={onNextPage} disabled={pageNumber >= numPages}>Next</button>
      </div>
      <canvas ref={canvasRef}></canvas>
    </div>
  );
};

export default PdfViewer;