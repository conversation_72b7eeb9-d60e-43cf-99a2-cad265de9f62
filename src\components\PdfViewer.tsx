import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Loader2, AlertCircle, Download, ExternalLink } from 'lucide-react';

interface PdfViewerProps {
  fileUrl: string;
  fileName?: string;
}

const PdfViewer: React.FC<PdfViewerProps> = ({ fileUrl, fileName = 'ملف PDF' }) => {
  const [viewMode, setViewMode] = useState<'iframe' | 'download' | 'external'>('iframe');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processedUrl, setProcessedUrl] = useState<string>('');

  // معالجة URL عند التحميل
  useEffect(() => {
    const processFileUrl = async () => {
      try {
        let newUrl = fileUrl;

        // إذا كان data URL، نحوله إلى blob URL
        if (fileUrl.startsWith('data:')) {
          console.log('تحويل data URL إلى blob URL...');
          const response = await fetch(fileUrl);
          const blob = await response.blob();
          newUrl = URL.createObjectURL(blob);
          console.log('تم إنشاء blob URL بنجاح');
        }

        setProcessedUrl(newUrl);
      } catch (error) {
        console.error('خطأ في معالجة URL:', error);
        setProcessedUrl(fileUrl); // استخدم الـ URL الأصلي كـ fallback
      }
    };

    processFileUrl();

    // تنظيف blob URL عند إلغاء المكون
    return () => {
      if (processedUrl && processedUrl !== fileUrl && processedUrl.startsWith('blob:')) {
        URL.revokeObjectURL(processedUrl);
      }
    };
  }, [fileUrl]);

  const handleDownload = () => {
    try {
      const link = document.createElement('a');
      link.href = processedUrl || fileUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('خطأ في تحميل الملف:', error);
      setError('فشل في تحميل الملف');
    }
  };

  const handleOpenInNewTab = () => {
    try {
      const urlToOpen = processedUrl || fileUrl;

      // إذا كان data URL، ننشئ صفحة HTML بسيطة تحتوي على iframe
      if (fileUrl.startsWith('data:')) {
        const htmlContent = `
          <!DOCTYPE html>
          <html dir="rtl" lang="ar">
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${fileName}</title>
            <style>
              body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
              .header { background: #f5f5f5; padding: 10px; border-bottom: 1px solid #ddd; text-align: center; }
              iframe { width: 100%; height: calc(100vh - 60px); border: none; }
              .fallback { text-align: center; padding: 50px; }
              .btn { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px; display: inline-block; }
            </style>
          </head>
          <body>
            <div class="header">
              <h3>${fileName}</h3>
            </div>
            <iframe src="${urlToOpen}" title="${fileName}"></iframe>
            <div class="fallback" style="display: none;" id="fallback">
              <h3>لا يمكن عرض PDF في هذا المتصفح</h3>
              <a href="${urlToOpen}" download="${fileName}" class="btn">تحميل الملف</a>
            </div>
            <script>
              // إظهار خيار التحميل إذا فشل iframe
              setTimeout(() => {
                const iframe = document.querySelector('iframe');
                const fallback = document.getElementById('fallback');
                if (iframe && fallback) {
                  iframe.onerror = () => {
                    iframe.style.display = 'none';
                    fallback.style.display = 'block';
                  };
                }
              }, 1000);
            </script>
          </body>
          </html>
        `;

        const htmlBlob = new Blob([htmlContent], { type: 'text/html' });
        const htmlUrl = URL.createObjectURL(htmlBlob);
        window.open(htmlUrl, '_blank');

        // تنظيف URL بعد فترة
        setTimeout(() => URL.revokeObjectURL(htmlUrl), 5000);
      } else {
        // للـ URLs العادية
        window.open(urlToOpen, '_blank');
      }
    } catch (error) {
      console.error('خطأ في فتح الملف:', error);
      setError('فشل في فتح الملف في نافذة جديدة');
    }
  };

  const handleIframeLoad = () => {
    setLoading(false);
  };

  const handleIframeError = () => {
    setError('فشل في تحميل ملف PDF في المتصفح');
    setLoading(false);
    setViewMode('download');
  };

  if (error && viewMode === 'iframe') {
    return (
      <div className="flex flex-col items-center justify-center h-64 p-6">
        <AlertCircle className="h-12 w-12 text-orange-500 mb-4" />
        <h3 className="text-lg font-medium mb-2">لا يمكن عرض PDF في المتصفح</h3>
        <p className="text-gray-600 text-center mb-4">
          متصفحك لا يدعم عرض ملفات PDF مباشرة. يمكنك تحميل الملف أو فتحه في نافذة جديدة.
        </p>
        <div className="flex gap-2">
          <Button onClick={handleDownload} className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            تحميل الملف
          </Button>
          <Button onClick={handleOpenInNewTab} variant="outline" className="flex items-center gap-2">
            <ExternalLink className="h-4 w-4" />
            فتح في نافذة جديدة
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* شريط التحكم */}
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">{fileName}</span>
          {loading && <Loader2 className="h-4 w-4 animate-spin" />}
        </div>

        <div className="flex gap-2">
          <Button onClick={handleDownload} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-1" />
            تحميل
          </Button>
          <Button onClick={handleOpenInNewTab} variant="outline" size="sm">
            <ExternalLink className="h-4 w-4 mr-1" />
            فتح في نافذة جديدة
          </Button>
        </div>
      </div>

      {/* منطقة عرض PDF */}
      <div className="flex-1 relative">
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
              <p>جاري تحميل ملف PDF...</p>
            </div>
          </div>
        )}

        <iframe
          src={processedUrl || fileUrl}
          className="w-full h-full border-0"
          title={fileName}
          onLoad={handleIframeLoad}
          onError={handleIframeError}
          style={{ minHeight: '500px' }}
        />
      </div>
    </div>
  );
};

export default PdfViewer;