import React, { useRef, useEffect, useState } from 'react';
import * as pdfjsLib from 'pdfjs-dist';
import 'pdfjs-dist/web/pdf_viewer.css';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Loader2, AlertCircle } from 'lucide-react';

// تعيين Worker - جرب عدة طرق للتأكد من العمل
try {
  // الطريقة الأولى: استخدام الملفات المحلية
  pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(
    'pdfjs-dist/build/pdf.worker.min.js',
    import.meta.url
  ).toString();
} catch (error) {
  // الطريقة البديلة: استخدام CDN
  pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
}

interface PdfViewerProps {
  fileUrl: string;
}

const PdfViewer: React.FC<PdfViewerProps> = ({ fileUrl }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [pdf, setPdf] = useState<pdfjsLib.PDFDocumentProxy | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [numPages, setNumPages] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pageLoading, setPageLoading] = useState(false);

  useEffect(() => {
    if (!fileUrl) {
      setError('لم يتم توفير رابط الملف');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    // معالجة خاصة لـ data URLs
    const processFileUrl = async () => {
      try {
        let processedUrl = fileUrl;

        // إذا كان الملف data URL، نحوله إلى blob URL لتحسين الأداء
        if (fileUrl.startsWith('data:')) {
          console.log('تحويل data URL إلى blob URL...');
          const response = await fetch(fileUrl);
          const blob = await response.blob();
          processedUrl = URL.createObjectURL(blob);
          console.log('تم إنشاء blob URL بنجاح');
        }

        const loadingTask = pdfjsLib.getDocument({
          url: processedUrl,
          // إعدادات إضافية لتحسين التوافق
          cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/cmaps/',
          cMapPacked: true,
          standardFontDataUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/standard_fonts/',
        });

        loadingTask.promise
          .then(pdfDoc => {
            setPdf(pdfDoc);
            setNumPages(pdfDoc.numPages);
            setLoading(false);
          })
          .catch(error => {
            console.error('خطأ في تحميل PDF:', error);
            setError(`فشل في تحميل ملف PDF: ${error.message || 'خطأ غير معروف'}`);
            setLoading(false);
          });

        // تنظيف blob URL عند انتهاء المكون
        return () => {
          if (processedUrl !== fileUrl && processedUrl.startsWith('blob:')) {
            URL.revokeObjectURL(processedUrl);
          }
          loadingTask.destroy();
        };

      } catch (error) {
        console.error('خطأ في معالجة URL:', error);
        setError(`خطأ في معالجة الملف: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
        setLoading(false);
      }
    };

    const cleanup = processFileUrl();

    return () => {
      if (cleanup instanceof Promise) {
        cleanup.then(cleanupFn => cleanupFn && cleanupFn());
      }
    };
  }, [fileUrl]);

  useEffect(() => {
    if (pdf && canvasRef.current) {
      setPageLoading(true);

      pdf.getPage(pageNumber)
        .then(page => {
          const canvas = canvasRef.current;
          if (canvas) {
            const context = canvas.getContext('2d');
            if (context) {
              // تنظيف الكانفاس قبل الرسم
              context.clearRect(0, 0, canvas.width, canvas.height);

              const viewport = page.getViewport({ scale: 1.5 });
              canvas.height = viewport.height;
              canvas.width = viewport.width;

              const renderContext = {
                canvasContext: context,
                viewport: viewport
              };

              return page.render(renderContext).promise;
            }
          }
        })
        .then(() => {
          setPageLoading(false);
        })
        .catch(error => {
          console.error('خطأ في رسم الصفحة:', error);
          setError('فشل في عرض الصفحة');
          setPageLoading(false);
        });
    }
  }, [pdf, pageNumber]);

  const onPrevPage = () => {
    if (pageNumber > 1) {
      setPageNumber(pageNumber - 1);
    }
  };

  const onNextPage = () => {
    if (pageNumber < numPages) {
      setPageNumber(pageNumber + 1);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p>جاري تحميل ملف PDF...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center text-red-500">
          <AlertCircle className="h-8 w-8 mx-auto mb-2" />
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* شريط التحكم */}
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <Button
          onClick={onPrevPage}
          disabled={pageNumber <= 1 || pageLoading}
          variant="outline"
          size="sm"
        >
          <ChevronRight className="h-4 w-4 mr-1" />
          السابق
        </Button>

        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">
            صفحة {pageNumber} من {numPages}
          </span>
          {pageLoading && <Loader2 className="h-4 w-4 animate-spin" />}
        </div>

        <Button
          onClick={onNextPage}
          disabled={pageNumber >= numPages || pageLoading}
          variant="outline"
          size="sm"
        >
          التالي
          <ChevronLeft className="h-4 w-4 ml-1" />
        </Button>
      </div>

      {/* منطقة عرض PDF */}
      <div className="flex-1 overflow-auto p-4 bg-gray-50">
        <div className="flex justify-center relative">
          <canvas
            ref={canvasRef}
            className="border shadow-lg bg-white max-w-full h-auto"
            style={{
              display: pageLoading ? 'none' : 'block',
              maxWidth: '100%',
              height: 'auto'
            }}
          />
          {pageLoading && (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                <p className="text-sm text-gray-600">جاري تحميل الصفحة...</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PdfViewer;