import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Loader2, AlertCircle, Download, ExternalLink } from 'lucide-react';

interface PdfViewerProps {
  fileUrl: string;
  fileName?: string;
}

const PdfViewer: React.FC<PdfViewerProps> = ({ fileUrl, fileName = 'ملف PDF' }) => {
  const [viewMode, setViewMode] = useState<'iframe' | 'download' | 'external'>('iframe');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // تحديد أفضل طريقة لعرض PDF
  const determineBestViewMode = () => {
    // إذا كان المتصفح يدعم عرض PDF مباشرة
    if (navigator.pdfViewerEnabled !== false) {
      return 'iframe';
    }
    return 'download';
  };

  const handleDownload = () => {
    try {
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('خطأ في تحميل الملف:', error);
      setError('فشل في تحميل الملف');
    }
  };

  const handleOpenInNewTab = () => {
    try {
      window.open(fileUrl, '_blank');
    } catch (error) {
      console.error('خطأ في فتح الملف:', error);
      setError('فشل في فتح الملف في نافذة جديدة');
    }
  };

  const handleIframeLoad = () => {
    setLoading(false);
  };

  const handleIframeError = () => {
    setError('فشل في تحميل ملف PDF في المتصفح');
    setLoading(false);
    setViewMode('download');
  };

  if (error && viewMode === 'iframe') {
    return (
      <div className="flex flex-col items-center justify-center h-64 p-6">
        <AlertCircle className="h-12 w-12 text-orange-500 mb-4" />
        <h3 className="text-lg font-medium mb-2">لا يمكن عرض PDF في المتصفح</h3>
        <p className="text-gray-600 text-center mb-4">
          متصفحك لا يدعم عرض ملفات PDF مباشرة. يمكنك تحميل الملف أو فتحه في نافذة جديدة.
        </p>
        <div className="flex gap-2">
          <Button onClick={handleDownload} className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            تحميل الملف
          </Button>
          <Button onClick={handleOpenInNewTab} variant="outline" className="flex items-center gap-2">
            <ExternalLink className="h-4 w-4" />
            فتح في نافذة جديدة
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* شريط التحكم */}
      <div className="flex items-center justify-between p-4 border-b bg-white">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">{fileName}</span>
          {loading && <Loader2 className="h-4 w-4 animate-spin" />}
        </div>

        <div className="flex gap-2">
          <Button onClick={handleDownload} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-1" />
            تحميل
          </Button>
          <Button onClick={handleOpenInNewTab} variant="outline" size="sm">
            <ExternalLink className="h-4 w-4 mr-1" />
            فتح في نافذة جديدة
          </Button>
        </div>
      </div>

      {/* منطقة عرض PDF */}
      <div className="flex-1 relative">
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
              <p>جاري تحميل ملف PDF...</p>
            </div>
          </div>
        )}

        <iframe
          src={fileUrl}
          className="w-full h-full border-0"
          title={fileName}
          onLoad={handleIframeLoad}
          onError={handleIframeError}
          style={{ minHeight: '500px' }}
        />
      </div>
    </div>
  );
};

export default PdfViewer;