// API Service للتعامل مع قاعدة البيانات المشتركة
// استخدام عنوان IP الديناميكي للشبكة
const getApiBaseUrl = () => {
  // إذا كان التطبيق يعمل على localhost، استخدم localhost
  if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    return 'http://localhost:3001';
  }
  // إذا كان يعمل على الشبكة، استخدم نفس IP الخادم
  return `http://${window.location.hostname}:3001`;
};

const API_BASE_URL = getApiBaseUrl();

export interface User {
  id: string;
  username: string;
  password: string;
  role: 'admin' | 'user';
  sectors: string[];
  permissions: {
    view: string[];
    download: string[];
    manage: string[];
  };
  fullName?: string;
  phoneNumber?: string;
}

export interface Sector {
  id: string;
  name: string;
}

export interface Category {
  id: string;
  name: string;
}

export interface Document {
  id: string;
  title: string;
  description: string;
  categoryId: string;
  sectorId: string;
  version: string;
  status: 'active' | 'archived';
  files: Array<{
    id: string;
    name: string;
    type: string;
    size: number;
    dateUploaded: string;
    data?: string;
  }>;
  dateCreated: string;
  createdBy: string;
  permissions: {
    view: string[];
    download: string[];
  };
}

class ApiService {
  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    try {
      const baseUrl = API_BASE_URL;
      console.log(`API Request: ${baseUrl}${endpoint}`);

      const response = await fetch(`${baseUrl}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options?.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status} - URL: ${baseUrl}${endpoint}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Users API
  async getUsers(): Promise<User[]> {
    return this.request<User[]>('/users');
  }

  async getUserById(id: string): Promise<User> {
    return this.request<User>(`/users/${id}`);
  }

  async createUser(user: Omit<User, 'id'>): Promise<User> {
    const newUser = { ...user, id: Date.now().toString() };
    return this.request<User>('/users', {
      method: 'POST',
      body: JSON.stringify(newUser),
    });
  }

  async updateUser(id: string, user: Partial<User>): Promise<User> {
    return this.request<User>(`/users/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(user),
    });
  }

  async deleteUser(id: string): Promise<void> {
    await this.request(`/users/${id}`, { method: 'DELETE' });
  }

  // Sectors API
  async getSectors(): Promise<Sector[]> {
    return this.request<Sector[]>('/sectors');
  }

  async createSector(sector: Omit<Sector, 'id'>): Promise<Sector> {
    const newSector = { ...sector, id: Date.now().toString() };
    return this.request<Sector>('/sectors', {
      method: 'POST',
      body: JSON.stringify(newSector),
    });
  }

  async updateSector(id: string, sector: Partial<Sector>): Promise<Sector> {
    return this.request<Sector>(`/sectors/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(sector),
    });
  }

  async deleteSector(id: string): Promise<void> {
    await this.request(`/sectors/${id}`, { method: 'DELETE' });
  }

  // Categories API
  async getCategories(): Promise<Category[]> {
    return this.request<Category[]>('/categories');
  }

  async createCategory(category: Omit<Category, 'id'>): Promise<Category> {
    const newCategory = { ...category, id: Date.now().toString() };
    return this.request<Category>('/categories', {
      method: 'POST',
      body: JSON.stringify(newCategory),
    });
  }

  async updateCategory(id: string, category: Partial<Category>): Promise<Category> {
    return this.request<Category>(`/categories/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(category),
    });
  }

  async deleteCategory(id: string): Promise<void> {
    await this.request(`/categories/${id}`, { method: 'DELETE' });
  }

  // Documents API
  async getDocuments(): Promise<Document[]> {
    return this.request<Document[]>('/documents');
  }

  async getDocumentById(id: string): Promise<Document> {
    return this.request<Document>(`/documents/${id}`);
  }

  async createDocument(document: Omit<Document, 'id'>): Promise<Document> {
    const newDocument = { 
      ...document, 
      id: Date.now().toString(),
      dateCreated: new Date().toISOString()
    };
    return this.request<Document>('/documents', {
      method: 'POST',
      body: JSON.stringify(newDocument),
    });
  }

  async updateDocument(id: string, document: Partial<Document>): Promise<Document> {
    return this.request<Document>(`/documents/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(document),
    });
  }

  async deleteDocument(id: string): Promise<void> {
    await this.request(`/documents/${id}`, { method: 'DELETE' });
  }

  // Authentication
  async authenticate(username: string, password: string): Promise<User | null> {
    try {
      const users = await this.getUsers();
      const user = users.find(u => u.username === username && u.password === password);
      return user || null;
    } catch (error) {
      console.error('Authentication failed:', error);
      return null;
    }
  }

  // Health check
  async isServerAvailable(): Promise<boolean> {
    try {
      await this.request('/users?_limit=1');
      return true;
    } catch (error) {
      return false;
    }
  }
}

export const apiService = new ApiService();
