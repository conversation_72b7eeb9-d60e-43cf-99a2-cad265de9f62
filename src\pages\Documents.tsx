import React, { useState, useEffect } from 'react';
import { useApp } from '@/contexts/AppContext';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Search, FileText, Filter, Download, Plus, FileUp, Shield, Eye, Settings, Loader2 } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Document, Category, Sector, User } from '@/contexts/AppContext';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

// Utility for IndexedDB storage estimation
const indexedDBStorage = {
  getStorageUsage: async (): Promise<{ used: number; available: number }> => {
    try {
      // Use the Storage API if available
      if (navigator.storage && navigator.storage.estimate) {
        const estimate = await navigator.storage.estimate();
        return {
          used: estimate.usage || 0,
          available: estimate.quota || 1024 * 1024 * 1024 // Default to 1GB if quota is undefined
        };
      }
      
      // Fallback to a default value
      return {
        used: 0,
        available: 1024 * 1024 * 1024 // 1GB
      };
    } catch (error) {
      console.error('Error estimating storage:', error);
      return {
        used: 0,
        available: 1024 * 1024 * 1024 // 1GB
      };
    }
  }
};

import PdfViewer from '@/components/PdfViewer';

const Documents: React.FC = () => {
  const {
    currentUser,
    documents,
    categories,
    sectors,
    users,
    isAdmin,
    addDocument,
  } = useApp();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedSector, setSelectedSector] = useState<string>('');
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isPdfViewerOpen, setIsPdfViewerOpen] = useState(false);
  const [pdfUrl, setPdfUrl] = useState('');
  const [newDocumentName, setNewDocumentName] = useState('');
  const [newDocumentCategory, setNewDocumentCategory] = useState('');
  const [newDocumentSector, setNewDocumentSector] = useState('');
  const [fileList, setFileList] = useState<File[]>([]);
  const [permissions, setPermissions] = useState<{
    view: string[];
    download: string[];
  }>({
    view: ['all'],
    download: ['all'],
  });
  
  // حالات التحميل والتقدم
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState('');

  // Filter documents based on search and filters
  useEffect(() => {
    let filtered = documents;

    // Filter by user permissions if not admin
    if (!isAdmin && currentUser) {
      filtered = filtered.filter(doc => {
        return currentUser.permissions.view.includes('all') ||
               currentUser.permissions.view.includes(doc.categoryId) ||
               currentUser.sectors.includes('all') ||
               currentUser.sectors.includes(doc.sectorId);
      });
    }

    // Apply search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(doc =>
        doc.name.toLowerCase().includes(term) ||
        doc.files.some(file => file.name.toLowerCase().includes(term))
      );
    }

    // Apply category filter
    if (selectedCategory && selectedCategory !== 'all') {
      filtered = filtered.filter(doc => doc.categoryId === selectedCategory);
    }

    // Apply sector filter
    if (selectedSector && selectedSector !== 'all') {
      filtered = filtered.filter(doc => doc.sectorId === selectedSector);
    }

    setFilteredDocuments(filtered);
  }, [documents, searchTerm, selectedCategory, selectedSector, isAdmin, currentUser]);

  const getCategoryName = (id: string) => {
    return categories.find(c => c.id === id)?.name || 'غير محدد';
  };

  const getSectorName = (id: string) => {
    return sectors.find(s => s.id === id)?.name || 'غير محدد';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    // تنسيق التاريخ الهجري
    const hijriDate = new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);

    // تنسيق التاريخ الميلادي
    const gregorianDate = new Intl.DateTimeFormat('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      calendar: 'gregory'
    }).format(date);

    return (
      <div className="flex flex-col gap-1">
        <span>{hijriDate}</span>
        <span className="text-xs text-muted-foreground">{gregorianDate}</span>
      </div>
    );
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFileList(Array.from(e.target.files));
    }
  };

  // ضغط الصور إذا كانت كبيرة
  const compressImage = (file: File, maxSizeMB: number = 2): Promise<File> => {
    return new Promise((resolve, reject) => {
      if (!file.type.startsWith('image/')) {
        resolve(file); // إذا لم يكن صورة، أرجع الملف كما هو
        return;
      }

      const canvas = window.document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // حساب الأبعاد الجديدة
        let { width, height } = img;
        const maxWidth = 1920;
        const maxHeight = 1080;

        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }

        canvas.width = width;
        canvas.height = height;

        // رسم الصورة المضغوطة
        ctx?.drawImage(img, 0, 0, width, height);

        // تحويل إلى blob مع جودة مضغوطة
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              
              console.log(`ضغط الصورة ${file.name}: ${(file.size / 1024 / 1024).toFixed(2)}MB → ${(compressedFile.size / 1024 / 1024).toFixed(2)}MB`);
              resolve(compressedFile);
            } else {
              resolve(file);
            }
          },
          file.type,
          0.7 // جودة 70%
        );
      };

      img.onerror = () => resolve(file);
      img.src = URL.createObjectURL(file);
    });
  };

  // التأكد من نوع الملف الصحيح
  const ensureCorrectMimeType = (file: File): File => {
    // التحقق من امتداد الملف للتأكد من نوع MIME الصحيح
    const fileName = file.name.toLowerCase();
    let correctType = file.type;
    
    // تصحيح نوع الملف بناءً على الامتداد
    if (fileName.endsWith('.pdf') && file.type !== 'application/pdf') {
      correctType = 'application/pdf';
      console.log(`تصحيح نوع الملف ${file.name} من ${file.type} إلى ${correctType}`);
    } else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) {
      correctType = 'image/jpeg';
    } else if (fileName.endsWith('.png')) {
      correctType = 'image/png';
    } else if (fileName.endsWith('.gif')) {
      correctType = 'image/gif';
    }
    
    // إذا كان النوع مختلفاً، أنشئ ملفاً جديداً بالنوع الصحيح
    if (correctType !== file.type) {
      return new File([file], file.name, { 
        type: correctType, 
        lastModified: file.lastModified 
      });
    }
    
    return file;
  };

  // وظيفة لتحويل الملف إلى Base64 مع ضغط للملفات الكبيرة
  const fileToBase64 = async (file: File): Promise<string> => {
    // تصحيح نوع الملف أولاً
    const fileWithCorrectType = ensureCorrectMimeType(file);
    
    // ضغط الملف إذا كان كبيراً
    let processedFile = fileWithCorrectType;
    if (file.size > 1 * 1024 * 1024) { // أكبر من 1 ميجابايت
      console.log(`الملف ${file.name} كبير (${(file.size / 1024 / 1024).toFixed(2)}MB)، محاولة ضغطه...`);
      processedFile = await compressImage(fileWithCorrectType, 2);
    }

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(processedFile);
      reader.onload = () => {
        if (reader.result) {
          console.log(`تم تحويل الملف بنجاح: ${file.name} (${(processedFile.size / 1024 / 1024).toFixed(2)}MB)`);
          
          // التأكد من أن URL يبدأ بنوع MIME الصحيح
          let result = reader.result as string;
          if (processedFile.type === 'application/pdf' && !result.startsWith('data:application/pdf;')) {
            result = result.replace(/^data:[^;]+;/, 'data:application/pdf;');
            console.log('تم تصحيح URL للملف PDF');
          }
          
          resolve(result);
        } else {
          reject(new Error("فشل في قراءة الملف"));
        }
      };
      reader.onerror = error => {
        console.error("خطأ في قراءة الملف:", error);
        reject(error);
      };
    });
  };

  // وظيفة للتحقق من صحة URL للملف
  const isValidFileUrl = (url: string): boolean => {
    return url.startsWith('data:') || url.startsWith('blob:') || url.startsWith('http');
  };

  // فحص سعة التخزين المتاحة (IndexedDB + localStorage)
  const checkStorageSpace = async (): Promise<{ used: number; total: number; percentage: number }> => {
    try {
      // فحص IndexedDB (المساحة الرئيسية للملفات)
      const indexedDBUsage = await indexedDBStorage.getStorageUsage();
      
      // فحص localStorage (للبيانات الأساسية)
      let localStorageUsed = 0;
      try {
        for (const key in localStorage) {
          if (localStorage.hasOwnProperty(key)) {
            localStorageUsed += localStorage[key].length + key.length;
          }
        }
      } catch (error) {
        console.error('خطأ في فحص localStorage:', error);
      }
      
      // IndexedDB عادة يدعم مساحات كبيرة جداً (جيجابايتات)
      const total = indexedDBUsage.available || 1024 * 1024 * 1024; // افتراض 1 جيجابايت كحد أدنى
      const used = (indexedDBUsage.used || 0) + localStorageUsed;
      const percentage = total > 0 ? (used / total) * 100 : 0;
      
      console.log(`سعة التخزين - المستخدم: ${(used / 1024 / 1024).toFixed(2)}MB، المتاح: ${(total / 1024 / 1024).toFixed(2)}MB (${percentage.toFixed(1)}%)`);
      
      return { used, total, percentage };
    } catch (error) {
      console.error('خطأ في فحص سعة التخزين:', error);
      // fallback إلى قيم افتراضية آمنة
      return { used: 0, total: 1024 * 1024 * 1024, percentage: 0 }; // 1GB افتراضي
    }
  };

  // معالجة الملفات تدريجياً لتجنب استهلاك الذاكرة
  const processFilesSequentially = async (files: File[]): Promise<any[]> => {
    const processedFiles: any[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      try {
        // تحديث حالة التقدم
        const progressPercent = Math.round(((i + 1) / files.length) * 100);
        setUploadProgress(progressPercent);
        setUploadStatus(`معالجة الملف ${i + 1} من ${files.length}: ${file.name}`);
        
        // السماح للواجهة بالتحديث
        await new Promise(resolve => setTimeout(resolve, 10));
        
        // تحويل الملف
        console.log(`معالجة الملف ${i + 1}/${files.length}: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} ميجابايت)`);
        const base64Data = await fileToBase64(file);
        
        // التحقق من صحة البيانات
        if (!base64Data || base64Data.length < 100) {
          throw new Error(`البيانات المحولة للملف ${file.name} غير صالحة`);
        }

        // تحديد النوع الصحيح للملف
        const fileType = file.name.toLowerCase().endsWith('.pdf') ? 'application/pdf' : file.type;
        
        const processedFile = {
          id: `file-${Date.now()}-${i}-${Math.random().toString(36).substring(2, 11)}`,
          name: file.name,
          url: base64Data,
          type: fileType, // استخدام النوع المصحح
          size: file.size,
          dateUploaded: new Date().toISOString()
        };

        processedFiles.push(processedFile);
        console.log(`تم معالجة الملف بنجاح: ${file.name}`);
        
        // تنظيف الذاكرة بين الملفات للملفات الكبيرة
        if (file.size > 5 * 1024 * 1024) { // إذا كان أكبر من 5 ميجابايت
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
      } catch (error) {
        console.error(`خطأ في معالجة الملف ${file.name}:`, error);
        setUploadStatus(`خطأ في معالجة الملف: ${file.name}`);
        throw new Error(`فشل في معالجة الملف ${file.name}: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
      }
    }
    
    return processedFiles;
  };

  const togglePermission = (type: 'view' | 'download', categoryId: string) => {
    const currentPermissions = permissions[type];

    if (categoryId === 'all') {
      setPermissions({
        ...permissions,
        [type]: currentPermissions.includes('all') ? [] : ['all']
      });
    } else {
      if (currentPermissions.includes('all')) {
        // If 'all' is currently selected, we need to replace it with all categories except 'all' itself
        const allCategoriesExceptAll = categories.map(cat => cat.id);
        const newPermissions = allCategoriesExceptAll.filter(id => id !== categoryId);
        setPermissions({
          ...permissions,
          [type]: newPermissions
        });
      } else if (currentPermissions.includes(categoryId)) {
        setPermissions({
          ...permissions,
          [type]: currentPermissions.filter(id => id !== categoryId)
        });
      } else {
        setPermissions({
          ...permissions,
          [type]: [...currentPermissions, categoryId]
        });
      }
    }
  };

  const handleAddDocument = async () => {
    if (!newDocumentName || !newDocumentCategory || !newDocumentSector || fileList.length === 0) {
      alert("يرجى ملء جميع الحقول المطلوبة وإرفاق ملف واحد على الأقل");
      return;
    }

    // منع الرفع المتعدد
    if (isUploading) {
      alert("جارٍ رفع ملفات أخرى، يرجى الانتظار...");
      return;
    }

    try {
      setIsUploading(true);
      setUploadProgress(0);
      setUploadStatus("بدء عملية رفع الملفات...");
      
      console.log("بدء عملية رفع الملفات...");
      console.log("عدد الملفات:", fileList.length);
      
      // فحص سعة التخزين المتاحة
      const storageInfo = await checkStorageSpace();
      
      if (storageInfo.percentage > 95) {
        setUploadStatus("تحذير: مساحة التخزين ممتلئة تقريباً. قد يفشل الرفع.");
        console.warn("مساحة التخزين ممتلئة تقريباً!");
      } else if (storageInfo.percentage > 80) {
        setUploadStatus("معلومة: مساحة التخزين تتجه نحو الامتلاء. فكر في حذف ملفات قديمة.");
        console.info("مساحة التخزين تتجه نحو الامتلاء");
      }
      
      // حساب إجمالي حجم الملفات
      const totalSize = fileList.reduce((sum, file) => sum + file.size, 0);
      const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);
      console.log(`إجمالي حجم الملفات: ${totalSizeMB} ميجابايت`);
      
      setUploadStatus(`معالجة ${fileList.length} ملف بحجم إجمالي ${totalSizeMB} ميجابايت...`);

      // استخدام المعالجة التدريجية الجديدة
      const newDocFiles = await processFilesSequentially(fileList);
      console.log("تم تحويل جميع الملفات بنجاح:", newDocFiles.length);

      // التحقق من صحة البيانات قبل الإضافة
      if (newDocFiles.length === 0) {
        throw new Error("لم يتم تحويل أي ملفات بنجاح");
      }

      // التحقق من صحة URLs للملفات
      for (const file of newDocFiles) {
        if (!isValidFileUrl(file.url)) {
          console.error(`URL غير صالح للملف ${file.name}:`, file.url.substring(0, 50) + '...');
          throw new Error(`URL غير صالح للملف ${file.name}`);
        }
      }

      // إضافة المستند
      const newDocument = {
        name: newDocumentName,
        categoryId: newDocumentCategory,
        sectorId: newDocumentSector,
        files: newDocFiles,
        permissions: permissions // Add permissions to the document
      };

      setUploadStatus("حفظ المستند...");
      console.log("إضافة المستند الجديد:", newDocument.name);
      
      // محاولة إضافة المستند مع معالجة الأخطاء
      await addDocument(newDocument);
      
      console.log("تم إضافة المستند بنجاح");

      setUploadStatus("تم الانتهاء بنجاح!");
      setUploadProgress(100);

      // إظهار رسالة نجاح بدون alert
      setUploadStatus(`تم إضافة المستند "${newDocumentName}" بنجاح مع ${newDocFiles.length} ملف!`);

      // Reset form
      setNewDocumentName('');
      setNewDocumentCategory('');
      setNewDocumentSector('');
      setFileList([]);
      setPermissions({ view: ['all'], download: ['all'] });
      
      // انتظار قليل لإظهار رسالة النجاح ثم إغلاق النافذة
      setTimeout(() => {
        setIsAddDialogOpen(false);
        setUploadProgress(0);
        setUploadStatus('');
      }, 2000);
      
    } catch (error) {
      console.error("خطأ في رفع الملفات:", error);
      setUploadStatus("حدث خطأ أثناء الرفع");
      alert("حدث خطأ أثناء رفع الملفات. يرجى المحاولة مرة أخرى: " + (error instanceof Error ? error.message : "خطأ غير معروف"));
    } finally {
      setIsUploading(false);
    }
  };

  // Group documents by category for viewing
  const documentsByCategory: { [key: string]: Document[] } = {};
  filteredDocuments.forEach(doc => {
    const categoryId = doc.categoryId;
    if (!documentsByCategory[categoryId]) {
      documentsByCategory[categoryId] = [];
    }
    documentsByCategory[categoryId].push(doc);
  });

  const canUserDownload = (document: Document) => {
    if (!currentUser) return false;
    if (isAdmin) return true;

    // Check document-specific permissions if they exist
    if (document.permissions) {
      if (document.permissions.download.includes('all')) return true;
      return document.permissions.download.includes(currentUser.id) ||
             currentUser.permissions.download.includes(document.categoryId) ||
             currentUser.permissions.download.includes('all');
    }

    // Fall back to user permissions
    return currentUser.permissions.download.includes('all') ||
           currentUser.permissions.download.includes(document.categoryId);
  };

  const canUserView = (document: Document) => {
    if (!currentUser) return false;
    if (isAdmin) return true;

    // Check document-specific permissions if they exist
    if (document.permissions) {
      if (document.permissions.view.includes('all')) return true;
      return document.permissions.view.includes(currentUser.id) ||
             currentUser.permissions.view.includes(document.categoryId) ||
             currentUser.permissions.view.includes('all');
    }

    // Fall back to user permissions
    return currentUser.permissions.view.includes('all') ||
           currentUser.permissions.view.includes(document.categoryId);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">المستندات</h1>

        {isAdmin && (
          <div className="flex gap-2">
            <Link to="/admin/document-files">
              <Button variant="outline">
                <Settings className="h-4 w-4 mr-2" /> إدارة الملفات
              </Button>
            </Link>
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" /> إضافة مستند جديد
                </Button>
              </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>إضافة مستند جديد</DialogTitle>
                <DialogDescription>
                  أدخل تفاصيل المستند الجديد وارفع الملفات المطلوبة.
                </DialogDescription>
              </DialogHeader>

              <Tabs defaultValue="details" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="details">تفاصيل المستند</TabsTrigger>
                  <TabsTrigger value="permissions">الصلاحيات</TabsTrigger>
                </TabsList>

                <TabsContent value="details">
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="name" className="text-right">
                        اسم المستند
                      </Label>
                      <Input
                        id="name"
                        value={newDocumentName}
                        onChange={(e) => setNewDocumentName(e.target.value)}
                        className="col-span-3"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="category" className="text-right">
                        التصنيف
                      </Label>
                      <Select value={newDocumentCategory} onValueChange={setNewDocumentCategory}>
                        <SelectTrigger className="col-span-3">
                          <SelectValue placeholder="اختر التصنيف" />
                        </SelectTrigger>
                        <SelectContent>
                          {categories.map(category => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="sector" className="text-right">
                        القطاع
                      </Label>
                      <Select value={newDocumentSector} onValueChange={setNewDocumentSector}>
                        <SelectTrigger className="col-span-3">
                          <SelectValue placeholder="اختر القطاع" />
                        </SelectTrigger>
                        <SelectContent>
                          {sectors.map(sector => (
                            <SelectItem key={sector.id} value={sector.id}>
                              {sector.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="files" className="text-right">
                        الملفات
                      </Label>
                      <div className="col-span-3">
                        <Input
                          id="files"
                          type="file"
                          multiple
                          onChange={handleFileChange}
                        />
                        <div className="text-sm text-muted-foreground mt-2">
                          {fileList.length > 0 ? (
                            <span>تم اختيار {fileList.length} ملفات</span>
                          ) : (
                            <span>اختر ملفًا واحدًا أو أكثر</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="permissions">
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-start gap-4">
                      <Label className="text-right pt-2 flex items-center gap-2">
                        <Shield className="h-4 w-4" /> صلاحيات العرض
                      </Label>
                      <div className="col-span-3 border rounded-md p-4">
                        <div className="flex items-center space-x-2 mb-3">
                          <Checkbox
                            id="view-all"
                            checked={permissions.view.includes('all')}
                            onCheckedChange={() => togglePermission('view', 'all')}
                          />
                          <label
                            htmlFor="view-all"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                          >
                            الجميع (كل المستخدمين)
                          </label>
                        </div>

                        {!permissions.view.includes('all') && (
                          <div className="grid grid-cols-2 gap-2">
                            {users.filter(user => user.role !== 'admin').map((user) => (
                              <div key={user.id} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`view-${user.id}`}
                                  checked={permissions.view.includes(user.id)}
                                  onCheckedChange={() => togglePermission('view', user.id)}
                                />
                                <label
                                  htmlFor={`view-${user.id}`}
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                                >
                                  {user.username}
                                </label>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-4 items-start gap-4">
                      <Label className="text-right pt-2 flex items-center gap-2">
                        <Download className="h-4 w-4" /> صلاحيات التحميل
                      </Label>
                      <div className="col-span-3 border rounded-md p-4">
                        <div className="flex items-center space-x-2 mb-3">
                          <Checkbox
                            id="download-all"
                            checked={permissions.download.includes('all')}
                            onCheckedChange={() => togglePermission('download', 'all')}
                          />
                          <label
                            htmlFor="download-all"
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                          >
                            الجميع (كل المستخدمين)
                          </label>
                        </div>

                        {!permissions.download.includes('all') && (
                          <div className="grid grid-cols-2 gap-2">
                            {users.filter(user => user.role !== 'admin').map((user) => (
                              <div key={user.id} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`download-${user.id}`}
                                  checked={permissions.download.includes(user.id)}
                                  onCheckedChange={() => togglePermission('download', user.id)}
                                />
                                <label
                                  htmlFor={`download-${user.id}`}
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mr-2"
                                >
                                  {user.username}
                                </label>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              {/* شريط التقدم أثناء الرفع */}
              {isUploading && (
                <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="text-sm font-medium">جارٍ رفع الملفات...</span>
                    </div>
                    <Progress value={uploadProgress} className="w-full" />
                    <p className="text-xs text-muted-foreground mt-2">
                      {uploadProgress}% - {uploadStatus}
                    </p>
                  </div>
                </div>
              )}

              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => setIsAddDialogOpen(false)}
                  disabled={isUploading}
                >
                  إلغاء
                </Button>
                <Button 
                  onClick={handleAddDocument}
                  disabled={isUploading || fileList.length === 0}
                >
                  {isUploading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      جارٍ الرفع...
                    </>
                  ) : (
                    'إضافة'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          </div>
        )}
      </div>

      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
            <div className="relative col-span-1 md:col-span-6">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="ابحث عن المستندات..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="col-span-1 md:col-span-3">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="تصفية حسب التصنيف" />
                </SelectTrigger>
                <SelectContent>
                  {/* Fix: Changed empty value to "all" */}
                  <SelectItem key="all-categories" value="all">كل التصنيفات</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="col-span-1 md:col-span-3">
              <Select value={selectedSector} onValueChange={setSelectedSector}>
                <SelectTrigger>
                  <SelectValue placeholder="تصفية حسب القطاع" />
                </SelectTrigger>
                <SelectContent>
                  {/* Fix: Changed empty value to "all" */}
                  <SelectItem key="all-sectors" value="all">كل القطاعات</SelectItem>
                  {sectors.map(sector => (
                    <SelectItem key={sector.id} value={sector.id}>
                      {sector.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {filteredDocuments.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-muted-foreground/40 mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">لا توجد مستندات</h3>
          <p className="text-muted-foreground">
            لم يتم العثور على أي مستندات تطابق معايير البحث الخاصة بك.
          </p>
        </div>
      ) : (
        <Accordion
          type="single"
          collapsible
          className="mb-6"
        >
          {categories
            .filter(category => documentsByCategory[category.id]?.length > 0)
            .map(category => (
              <AccordionItem value={category.id} key={category.id}>
                <AccordionTrigger className="hover:bg-accent hover:no-underline px-4">
                  <div className="flex items-center">
                    <FileText className="h-5 w-5 mr-2" />
                    <span>{category.name}</span>
                    <span className="ml-2 bg-primary/10 text-primary px-2 py-0.5 rounded-full text-xs">
                      {documentsByCategory[category.id]?.length || 0}
                    </span>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>اسم المستند</TableHead>
                          <TableHead>القطاع</TableHead>
                          <TableHead>تاريخ الإنشاء</TableHead>
                          <TableHead>عدد الملفات</TableHead>
                          <TableHead>الإجراءات</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {documentsByCategory[category.id]?.map(document => (
                          <TableRow key={document.id}>
                            <TableCell className="font-medium">{document.name}</TableCell>
                            <TableCell>{getSectorName(document.sectorId)}</TableCell>
                            <TableCell>{formatDate(document.dateCreated)}</TableCell>
                            <TableCell>{document.files.length}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                {canUserView(document) && (
                                  <Button variant="outline" size="sm" onClick={() => {
                                    try {
                                      if (document.files.length === 1) {
                                        // إذا كان هناك ملف واحد فقط، افتح الملف مباشرة
                                        const file = document.files[0];
                                        console.log("فتح الملف:", file.name);
                                        console.log("URL الملف:", file.url.substring(0, 50) + '...');

                                        // التحقق من صحة URL
                                        if (!isValidFileUrl(file.url)) {
                                          alert(`عذراً، URL الملف غير صالح: ${file.name}`);
                                          return;
                                        }

                                        // تحقق من صحة URL للملفات PDF
                                        if (file.type === 'application/pdf' && !file.url.startsWith('data:application/pdf;')) {
                                          console.warn('تنسيق URL للملف PDF غير صحيح:', file.url.substring(0, 50) + '...');
                                          // محاولة إصلاح URL إذا كان ناقصاً
                                          if (file.url.startsWith('data:') && !file.url.startsWith('data:application/pdf;')) {
                                            file.url = file.url.replace(/^data:[^;]+;/, 'data:application/pdf;');
                                            console.log('تم تصحيح URL للملف PDF');
                                          }
                                        }
                                        
                                        // عرض الملف بطريقة مناسبة حسب نوعه
                                        if (file.type === 'application/pdf') {
                                          setPdfUrl(file.url);
                                          setIsPdfViewerOpen(true);
                                        } else if (file.type.startsWith('image/')) {
                                          // للصور، نستخدم نافذة مخصصة لعرض الصورة
                                          const newWindow = window.open('', '_blank');
                                          if (newWindow) {
                                            newWindow.document.write(`
                                              <html>
                                                <head>
                                                  <title>${file.name}</title>
                                                  <style>
                                                    body, html { margin: 0; padding: 0; height: 100%; background-color: #f5f5f5; }
                                                    .container { display: flex; justify-content: center; align-items: center; height: 100%; flex-direction: column; }
                                                    .file-viewer { max-width: 95%; max-height: 85vh; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
                                                    h2 { font-family: Arial, sans-serif; margin-bottom: 20px; color: #333; }
                                                  </style>
                                                </head>
                                                <body>
                                                  <div class="container">
                                                    <h2>${file.name}</h2>
                                                    <img src="${file.url}" class="file-viewer" />
                                                  </div>
                                                </body>
                                              </html>
                                            `);
                                            newWindow.document.close();
                                          } else {
                                            alert("تم منع فتح النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة لهذا الموقع.");
                                          }
                                        } else {
                                          // لأنواع الملفات الأخرى، نقدم خيار التحميل
                                          const downloadLink = window.document.createElement('a');
                                          downloadLink.href = file.url;
                                          downloadLink.download = file.name;
                                          downloadLink.target = '_blank';
                                          window.document.body.appendChild(downloadLink);
                                          downloadLink.click();
                                          window.document.body.removeChild(downloadLink);
                                        }
                                      } else if (document.files.length > 1) {
                                        // إذا كان هناك أكثر من ملف، اعرض قائمة بالملفات
                                        const filesList = document.files.map((file, index) =>
                                          `${index + 1}. ${file.name}`
                                        ).join('\n');

                                        const fileIndex = parseInt(prompt(`اختر رقم الملف للعرض:\n${filesList}`) || '0') - 1;

                                        if (fileIndex >= 0 && fileIndex < document.files.length) {
                                          const file = document.files[fileIndex];
                                          console.log("فتح الملف:", file.name);

                                          // التحقق من صحة URL
                                          if (!isValidFileUrl(file.url)) {
                                            alert(`عذراً، URL الملف غير صالح: ${file.name}`);
                                            return;
                                          }

                                          // تحقق من صحة URL للملفات PDF
                                          if (file.type === 'application/pdf' && !file.url.startsWith('data:application/pdf;')) {
                                            console.warn('تنسيق URL للملف PDF غير صحيح:', file.url.substring(0, 50) + '...');
                                            // محاولة إصلاح URL إذا كان ناقصاً
                                            if (file.url.startsWith('data:') && !file.url.startsWith('data:application/pdf;')) {
                                              file.url = file.url.replace(/^data:[^;]+;/, 'data:application/pdf;');
                                              console.log('تم تصحيح URL للملف PDF');
                                            }
                                          }
                                          
                                          // عرض الملف بطريقة مناسبة حسب نوعه
                                          if (file.type === 'application/pdf') {
                                            setPdfUrl(file.url);
                                            setIsPdfViewerOpen(true);
                                          } else if (file.type.startsWith('image/')) {
                                            // للصور، نستخدم نافذة مخصصة لعرض الصورة
                                            const newWindow = window.open('', '_blank');
                                            if (newWindow) {
                                              newWindow.document.write(`
                                                <html>
                                                  <head>
                                                    <title>${file.name}</title>
                                                    <style>
                                                      body, html { margin: 0; padding: 0; height: 100%; background-color: #f5f5f5; }
                                                      .container { display: flex; justify-content: center; align-items: center; height: 100%; flex-direction: column; }
                                                      .file-viewer { max-width: 95%; max-height: 85vh; box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
                                                      h2 { font-family: Arial, sans-serif; margin-bottom: 20px; color: #333; }
                                                    </style>
                                                  </head>
                                                  <body>
                                                    <div class="container">
                                                      <h2>${file.name}</h2>
                                                      <img src="${file.url}" class="file-viewer" />
                                                    </div>
                                                  </body>
                                                </html>
                                              `);
                                              newWindow.document.close();
                                            } else {
                                              alert("تم منع فتح النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة لهذا الموقع.");
                                            }
                                          } else {
                                            // لأنواع الملفات الأخرى، نقدم خيار التحميل
                                            const downloadLink = window.document.createElement('a');
                                            downloadLink.href = file.url;
                                            downloadLink.download = file.name;
                                            downloadLink.target = '_blank';
                                            window.document.body.appendChild(downloadLink);
                                            downloadLink.click();
                                            window.document.body.removeChild(downloadLink);
                                          }
                                        }
                                      }
                                    } catch (error) {
                                      console.error("خطأ في عرض الملف:", error);
                                      alert("حدث خطأ أثناء محاولة عرض الملف. يرجى المحاولة مرة أخرى.");
                                    }
                                  }}>
                                    <Eye className="h-4 w-4 mr-1" /> عرض
                                  </Button>
                                )}

                                {canUserDownload(document) && (
                                  <Button variant="outline" size="sm" onClick={() => {
                                    if (document.files.length === 1) {
                                      // إذا كان هناك ملف واحد فقط، قم بتحميله مباشرة
                                      const file = document.files[0];
                                      const link = window.document.createElement('a');
                                      link.href = file.url;
                                      link.download = file.name;
                                      window.document.body.appendChild(link);
                                      link.click();
                                      window.document.body.removeChild(link);
                                    } else if (document.files.length > 1) {
                                      // إذا كان هناك أكثر من ملف، اعرض قائمة بالملفات
                                      const filesList = document.files.map((file, index) =>
                                        `${index + 1}. ${file.name}`
                                      ).join('\n');

                                      const fileIndex = parseInt(prompt(`اختر رقم الملف للتحميل:\n${filesList}`) || '0') - 1;

                                      if (fileIndex >= 0 && fileIndex < document.files.length) {
                                        const file = document.files[fileIndex];
                                        const link = window.document.createElement('a');
                                        link.href = file.url;
                                        link.download = file.name;
                                        window.document.body.appendChild(link);
                                        link.click();
                                        window.document.body.removeChild(link);
                                      }
                                    }
                                  }}>
                                    <Download className="h-4 w-4 mr-1" /> تحميل
                                  </Button>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
        </Accordion>
      )}

      <Dialog open={isPdfViewerOpen} onOpenChange={setIsPdfViewerOpen}>
        <DialogContent className="max-w-4xl h-[90vh]">
          <PdfViewer fileUrl={pdfUrl} />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Documents;
