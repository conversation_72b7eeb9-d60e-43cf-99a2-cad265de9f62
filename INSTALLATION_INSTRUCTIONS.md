# تعليمات التثبيت والتشغيل

## المتطلبات
- Node.js (إصدار 18 أو أحدث)
- npm أو yarn

## التثبيت
1. تأكد من تثبيت المتطلبات
2. قم بتشغيل الأمر التالي في terminal:
```bash
npm install
```

## التشغيل
```bash
npm run dev
```

## الوصول للتطبيق
- افتح المتصفح واذهب إلى: `http://localhost:8080`
- للدخول كمدير استخدم:
  - اسم المستخدم: `admin`
  - كلمة المرور: `admin123`

## الميزات الجديدة المضافة

### 1. صفحة إدارة الملفات
- مخصصة للمديرين فقط
- إدارة شاملة للملفات والمستندات
- إحصائيات مفصلة

### 2. وظائف إدارة الملفات
- تعديل أسماء الملفات
- حذف الملفات
- إضافة ملفات جديدة
- إعادة ترتيب الملفات
- عرض معلومات مفصلة

### 3. إدارة المستندات
- تعديل معلومات المستند
- حذف المستند كاملاً
- إدارة الصلاحيات

### 4. البحث والتصفية
- بحث متقدم في المستندات والملفات
- تصفية حسب التصنيف والقطاع
- عرض الإحصائيات

## الاستخدام
راجع ملف `ADMIN_FILE_MANAGEMENT_GUIDE.md` للتعليمات التفصيلية.