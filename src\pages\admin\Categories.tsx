
import React, { useState } from 'react';
import { useApp } from '@/contexts/AppContext';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Plus, Trash, Edit } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Category } from '@/contexts/AppContext';

const Categories: React.FC = () => {
  const { categories, addCategory, updateCategory, deleteCategory, documents } = useApp();
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);

  const handleAddCategory = () => {
    if (!newCategoryName) return;
    
    addCategory(newCategoryName);
    setNewCategoryName('');
    setIsAddDialogOpen(false);
  };

  const handleEditCategory = () => {
    if (!selectedCategory || !newCategoryName) return;
    
    updateCategory(selectedCategory.id, newCategoryName);
    setSelectedCategory(null);
    setNewCategoryName('');
    setIsEditDialogOpen(false);
  };

  const handleEdit = (category: Category) => {
    setSelectedCategory(category);
    setNewCategoryName(category.name);
    setIsEditDialogOpen(true);
  };

  const handleDeleteCategory = (categoryId: string) => {
    // Check if there are any documents using this category
    const hasDocuments = documents.some(doc => doc.categoryId === categoryId);
    
    if (hasDocuments) {
      alert('لا يمكن حذف هذا التصنيف لأنه مستخدم في مستندات');
      return;
    }
    
    if (confirm('هل أنت متأكد من حذف هذا التصنيف؟')) {
      deleteCategory(categoryId);
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">إدارة التصنيفات</h1>
        
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" /> إضافة تصنيف جديد
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>إضافة تصنيف جديد</DialogTitle>
              <DialogDescription>
                أدخل اسم التصنيف الجديد.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  اسم التصنيف
                </Label>
                <Input
                  id="name"
                  value={newCategoryName}
                  onChange={(e) => setNewCategoryName(e.target.value)}
                  className="col-span-3"
                  placeholder="أدخل اسم التصنيف"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                إلغاء
              </Button>
              <Button onClick={handleAddCategory}>إضافة</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        
        {/* Edit Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>تعديل التصنيف</DialogTitle>
              <DialogDescription>
                تعديل اسم التصنيف.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-name" className="text-right">
                  اسم التصنيف
                </Label>
                <Input
                  id="edit-name"
                  value={newCategoryName}
                  onChange={(e) => setNewCategoryName(e.target.value)}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                إلغاء
              </Button>
              <Button onClick={handleEditCategory}>حفظ التغييرات</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      
      <Card className="p-6 mb-6">
        <p className="text-muted-foreground">
          التصنيفات هي أنواع المستندات المختلفة مثل إجراءات العمل، تعليمات العمل، خطط الفحص، وغيرها.
        </p>
      </Card>
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>اسم التصنيف</TableHead>
              <TableHead>عدد المستندات</TableHead>
              <TableHead>الإجراءات</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {categories.map(category => {
              const categoryDocumentsCount = documents.filter(doc => doc.categoryId === category.id).length;
              
              return (
                <TableRow key={category.id}>
                  <TableCell className="font-medium">{category.name}</TableCell>
                  <TableCell>{categoryDocumentsCount}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="icon" onClick={() => handleEdit(category)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleDeleteCategory(category.id)}
                        disabled={categoryDocumentsCount > 0}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

// Simple Label component for the form
const Label: React.FC<{
  htmlFor: string;
  className?: string;
  children: React.ReactNode;
}> = ({ htmlFor, className, children }) => (
  <label
    htmlFor={htmlFor}
    className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${className}`}
  >
    {children}
  </label>
);

export default Categories;
